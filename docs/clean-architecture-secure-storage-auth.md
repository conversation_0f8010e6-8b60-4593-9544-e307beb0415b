# Clean Architecture: Secure Storage & Authentication

**Status**: Draft v1  
**Owner**: Architecture Team  
**Last Updated**: 2025-08-20

## Overview

This document outlines the Clean Architecture approach for handling secure storage and authentication across mobile and web platforms. The architecture uses dependency injection to provide platform-specific implementations while maintaining consistent business logic.

## Architecture Principles

### Core Tenets
- **Dependency Inversion**: Services depend on abstractions, not concrete implementations
- **Platform Abstraction**: Business logic remains platform-agnostic
- **Single Responsibility**: Each layer has a distinct purpose
- **Testability**: All components can be tested in isolation

### Layer Dependencies
```
UI Layer (Screens/Components)
    ↓
Hooks Layer
    ↓
Services Layer (Business Logic)
    ↓
Repository Layer (Data Access)
    ↓
Platform Layer (Native APIs/Web APIs)
```

## Domain Models

### Authentication Domain
```typescript
// Core authentication entity
export interface AuthenticationData {
  accessToken: string;
  refreshToken?: string;
  idToken?: string;
  expiresAt: number;
  tokenType: 'Bearer';
  scope?: string[];
}

// User profile from authentication
export interface AuthenticatedUser {
  id: string;
  email: string;
  name: string;
  roles: string[];
  tenant?: string;
  groups?: string[];
}

// Authentication state
export interface AuthenticationState {
  isAuthenticated: boolean;
  user: AuthenticatedUser | null;
  isLoading: boolean;
  error: string | null;
}
```

### Secure Storage Domain
```typescript
// Generic secure storage item
export interface SecureStorageItem<T = any> {
  key: string;
  value: T;
  expiresAt?: number;
  metadata?: Record<string, any>;
}

// Storage operation result
export interface StorageResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}
```

## Repository Layer (Data Access)

### Abstract Interfaces
```typescript
// Core secure storage contract
export interface ISecureStorageRepository {
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
  
  // Typed object operations
  setObject<T>(key: string, value: T): Promise<void>;
  getObject<T>(key: string): Promise<T | null>;
  removeObject(key: string): Promise<void>;
}

// Authentication-specific storage contract
export interface IAuthStorageRepository extends ISecureStorageRepository {
  saveAuthData(authData: AuthenticationData): Promise<void>;
  getAuthData(): Promise<AuthenticationData | null>;
  clearAuthData(): Promise<void>;
  
  saveUserProfile(user: AuthenticatedUser): Promise<void>;
  getUserProfile(): Promise<AuthenticatedUser | null>;
}

// Session storage contract (web-specific)
export interface ISessionStorageRepository {
  setSessionItem(key: string, value: string): Promise<void>;
  getSessionItem(key: string): Promise<string | null>;
  removeSessionItem(key: string): Promise<void>;
  clearSession(): Promise<void>;
}
```

### Platform Implementations

#### Mobile Implementation
```typescript
// src/repositories/secureStorage/mobileSecureStorageRepository.ts
import * as SecureStore from 'expo-secure-store';

export class MobileSecureStorageRepository implements IAuthStorageRepository {
  async setItem(key: string, value: string): Promise<void> {
    await SecureStore.setItemAsync(key, value);
  }

  async getItem(key: string): Promise<string | null> {
    return await SecureStore.getItemAsync(key);
  }

  async removeItem(key: string): Promise<void> {
    await SecureStore.deleteItemAsync(key);
  }

  async clear(): Promise<void> {
    // Implementation depends on key management strategy
    const keys = await this.getAllKeys();
    await Promise.all(keys.map(key => this.removeItem(key)));
  }

  async setObject<T>(key: string, value: T): Promise<void> {
    await this.setItem(key, JSON.stringify(value));
  }

  async getObject<T>(key: string): Promise<T | null> {
    const json = await this.getItem(key);
    return json ? JSON.parse(json) : null;
  }

  async saveAuthData(authData: AuthenticationData): Promise<void> {
    await this.setObject('auth_data', authData);
  }

  async getAuthData(): Promise<AuthenticationData | null> {
    return await this.getObject<AuthenticationData>('auth_data');
  }

  async clearAuthData(): Promise<void> {
    await this.removeItem('auth_data');
    await this.removeItem('user_profile');
  }

  async saveUserProfile(user: AuthenticatedUser): Promise<void> {
    await this.setObject('user_profile', user);
  }

  async getUserProfile(): Promise<AuthenticatedUser | null> {
    return await this.getObject<AuthenticatedUser>('user_profile');
  }

  private async getAllKeys(): Promise<string[]> {
    // Implementation depends on your key tracking strategy
    return ['auth_data', 'user_profile']; // Simplified
  }
}
```

#### Web Implementation
```typescript
// src/repositories/secureStorage/webSecureStorageRepository.ts
export class WebSecureStorageRepository implements IAuthStorageRepository {
  // Use sessionStorage for sensitive data (auto-clears on tab close)
  async setItem(key: string, value: string): Promise<void> {
    sessionStorage.setItem(key, value);
  }

  async getItem(key: string): Promise<string | null> {
    return sessionStorage.getItem(key);
  }

  async removeItem(key: string): Promise<void> {
    sessionStorage.removeItem(key);
  }

  async clear(): Promise<void> {
    sessionStorage.clear();
  }

  async setObject<T>(key: string, value: T): Promise<void> {
    await this.setItem(key, JSON.stringify(value));
  }

  async getObject<T>(key: string): Promise<T | null> {
    const json = await this.getItem(key);
    return json ? JSON.parse(json) : null;
  }

  // Auth data is managed by Okta on web - these methods handle session data only
  async saveAuthData(authData: AuthenticationData): Promise<void> {
    // Store minimal session data, not the actual tokens (Okta manages those)
    const sessionData = {
      expiresAt: authData.expiresAt,
      tokenType: authData.tokenType,
      scope: authData.scope
    };
    await this.setObject('auth_session', sessionData);
  }

  async getAuthData(): Promise<AuthenticationData | null> {
    // This will be supplemented by Okta SDK in the service layer
    return await this.getObject<AuthenticationData>('auth_session');
  }

  async clearAuthData(): Promise<void> {
    await this.removeItem('auth_session');
    await this.removeItem('user_profile');
  }

  async saveUserProfile(user: AuthenticatedUser): Promise<void> {
    await this.setObject('user_profile', user);
  }

  async getUserProfile(): Promise<AuthenticatedUser | null> {
    return await this.getObject<AuthenticatedUser>('user_profile');
  }
}

// Web-specific session storage for temporary data
export class WebSessionStorageRepository implements ISessionStorageRepository {
  async setSessionItem(key: string, value: string): Promise<void> {
    sessionStorage.setItem(key, value);
  }

  async getSessionItem(key: string): Promise<string | null> {
    return sessionStorage.getItem(key);
  }

  async removeSessionItem(key: string): Promise<void> {
    sessionStorage.removeItem(key);
  }

  async clearSession(): Promise<void> {
    sessionStorage.clear();
  }
}

// Web preferences storage (non-sensitive, persistent)
export class WebPreferencesRepository {
  async setPreference(key: string, value: string): Promise<void> {
    localStorage.setItem(`pref_${key}`, value);
  }

  async getPreference(key: string): Promise<string | null> {
    return localStorage.getItem(`pref_${key}`);
  }

  async removePreference(key: string): Promise<void> {
    localStorage.removeItem(`pref_${key}`);
  }
}
```

### Authentication Provider Interfaces
```typescript
// Abstract authentication provider
export interface IAuthenticationProvider {
  signIn(): Promise<AuthenticationData>;
  signOut(): Promise<void>;
  refreshToken(): Promise<AuthenticationData>;
  isAuthenticated(): Promise<boolean>;
  getCurrentUser(): Promise<AuthenticatedUser | null>;
  getAccessToken(): Promise<string | null>;
}

// Okta-specific provider (web)
export interface IOktaAuthProvider extends IAuthenticationProvider {
  getOktaAuthClient(): any; // OktaAuth instance
  handleCallback(): Promise<AuthenticationData>;
}

// Expo Auth Session provider (mobile)
export interface IExpoAuthProvider extends IAuthenticationProvider {
  makeRedirectUri(): string;
  getAuthRequestConfig(): any;
}
```

## Services Layer (Business Logic)

### Authentication Service
```typescript
// src/services/authenticationService.ts
export class AuthenticationService {
  constructor(
    private authProvider: IAuthenticationProvider,
    private storageRepository: IAuthStorageRepository,
    private sessionRepository?: ISessionStorageRepository
  ) {}

  async signIn(): Promise<AuthenticationState> {
    try {
      const authData = await this.authProvider.signIn();
      const user = await this.authProvider.getCurrentUser();

      if (user) {
        await this.storageRepository.saveAuthData(authData);
        await this.storageRepository.saveUserProfile(user);
      }

      return {
        isAuthenticated: true,
        user,
        isLoading: false,
        error: null
      };
    } catch (error) {
      return {
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  async signOut(): Promise<void> {
    await this.authProvider.signOut();
    await this.storageRepository.clearAuthData();
    if (this.sessionRepository) {
      await this.sessionRepository.clearSession();
    }
  }

  async refreshAuthIfNeeded(): Promise<boolean> {
    try {
      const authData = await this.storageRepository.getAuthData();
      if (!authData) return false;

      // Check if token expires within 5 minutes
      const fiveMinutesFromNow = Date.now() + 5 * 60 * 1000;
      const needsRefresh = authData.expiresAt <= fiveMinutesFromNow;

      if (needsRefresh) {
        const newAuthData = await this.authProvider.refreshToken();
        await this.storageRepository.saveAuthData(newAuthData);
        return true;
      }

      return true;
    } catch (error) {
      console.error('Failed to refresh authentication:', error);
      return false;
    }
  }

  async getCurrentAuthState(): Promise<AuthenticationState> {
    try {
      const isAuthenticated = await this.authProvider.isAuthenticated();
      const user = await this.storageRepository.getUserProfile();

      return {
        isAuthenticated,
        user,
        isLoading: false,
        error: null
      };
    } catch (error) {
      return {
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to get auth state'
      };
    }
  }

  async getAccessToken(): Promise<string | null> {
    return await this.authProvider.getAccessToken();
  }
}
```

### Secure Storage Service
```typescript
// src/services/secureStorageService.ts
export class SecureStorageService {
  constructor(
    private storageRepository: ISecureStorageRepository
  ) {}

  async storeSecureData<T>(key: string, data: T): Promise<StorageResult<void>> {
    try {
      // Business validation
      if (!key || key.trim().length === 0) {
        throw new Error('Storage key cannot be empty');
      }

      if (data === null || data === undefined) {
        throw new Error('Cannot store null or undefined data');
      }

      await this.storageRepository.setObject(key, data);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Storage operation failed'
      };
    }
  }

  async getSecureData<T>(key: string): Promise<StorageResult<T>> {
    try {
      if (!key || key.trim().length === 0) {
        throw new Error('Storage key cannot be empty');
      }

      const data = await this.storageRepository.getObject<T>(key);
      return { success: true, data: data || undefined };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Retrieval operation failed'
      };
    }
  }

  async removeSecureData(key: string): Promise<StorageResult<void>> {
    try {
      if (!key || key.trim().length === 0) {
        throw new Error('Storage key cannot be empty');
      }

      await this.storageRepository.removeObject(key);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Removal operation failed'
      };
    }
  }

  async clearAllData(): Promise<StorageResult<void>> {
    try {
      await this.storageRepository.clear();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Clear operation failed'
      };
    }
  }
}
```

## Dependency Injection & Factory Pattern

### Service Factory
```typescript
// src/services/serviceFactory.ts
import { Platform } from 'react-native';

export class ServiceFactory {
  private static authService: AuthenticationService | null = null;
  private static storageService: SecureStorageService | null = null;

  static createAuthenticationService(): AuthenticationService {
    if (!this.authService) {
      const authProvider = this.createAuthProvider();
      const storageRepository = this.createAuthStorageRepository();
      const sessionRepository = Platform.OS === 'web'
        ? this.createSessionStorageRepository()
        : undefined;

      this.authService = new AuthenticationService(
        authProvider,
        storageRepository,
        sessionRepository
      );
    }
    return this.authService;
  }

  static createSecureStorageService(): SecureStorageService {
    if (!this.storageService) {
      const storageRepository = this.createSecureStorageRepository();
      this.storageService = new SecureStorageService(storageRepository);
    }
    return this.storageService;
  }

  private static createAuthProvider(): IAuthenticationProvider {
    if (Platform.OS === 'web') {
      return new OktaWebAuthProvider();
    } else {
      return new ExpoAuthProvider();
    }
  }

  private static createAuthStorageRepository(): IAuthStorageRepository {
    if (Platform.OS === 'web') {
      return new WebSecureStorageRepository();
    } else {
      return new MobileSecureStorageRepository();
    }
  }

  private static createSecureStorageRepository(): ISecureStorageRepository {
    if (Platform.OS === 'web') {
      return new WebSecureStorageRepository();
    } else {
      return new MobileSecureStorageRepository();
    }
  }

  private static createSessionStorageRepository(): ISessionStorageRepository {
    return new WebSessionStorageRepository();
  }

  // For testing - allows injection of mock dependencies
  static createTestAuthenticationService(
    authProvider: IAuthenticationProvider,
    storageRepository: IAuthStorageRepository,
    sessionRepository?: ISessionStorageRepository
  ): AuthenticationService {
    return new AuthenticationService(authProvider, storageRepository, sessionRepository);
  }

  static createTestSecureStorageService(
    storageRepository: ISecureStorageRepository
  ): SecureStorageService {
    return new SecureStorageService(storageRepository);
  }

  // Reset singletons (useful for testing)
  static reset(): void {
    this.authService = null;
    this.storageService = null;
  }
}
```

## Platform-Specific Authentication Providers

### Okta Web Provider
```typescript
// src/repositories/auth/oktaWebAuthProvider.ts
import { OktaAuth } from '@okta/okta-auth-js';

export class OktaWebAuthProvider implements IOktaAuthProvider {
  private oktaAuth: OktaAuth;

  constructor() {
    this.oktaAuth = new OktaAuth({
      issuer: process.env.EXPO_PUBLIC_OKTA_ISSUER!,
      clientId: process.env.EXPO_PUBLIC_OKTA_CLIENT_ID!,
      redirectUri: `${window.location.origin}/callback`,
      scopes: ['openid', 'profile', 'email', 'offline_access'],
      pkce: true,
      restoreOriginalUri: async (oktaAuth, originalUri) => {
        window.location.replace(originalUri || '/');
      }
    });
  }

  async signIn(): Promise<AuthenticationData> {
    await this.oktaAuth.signInWithRedirect();
    // This will redirect, so we won't reach here in normal flow
    throw new Error('Redirect in progress');
  }

  async signOut(): Promise<void> {
    await this.oktaAuth.signOut();
  }

  async refreshToken(): Promise<AuthenticationData> {
    const tokenManager = this.oktaAuth.tokenManager;
    await tokenManager.renew('accessToken');

    const accessToken = await tokenManager.get('accessToken');
    const idToken = await tokenManager.get('idToken');

    if (!accessToken) {
      throw new Error('Failed to refresh access token');
    }

    return {
      accessToken: accessToken.accessToken,
      idToken: idToken?.idToken,
      expiresAt: accessToken.expiresAt * 1000, // Convert to milliseconds
      tokenType: 'Bearer',
      scope: accessToken.scopes
    };
  }

  async isAuthenticated(): Promise<boolean> {
    return await this.oktaAuth.isAuthenticated();
  }

  async getCurrentUser(): Promise<AuthenticatedUser | null> {
    try {
      const user = await this.oktaAuth.getUser();
      if (!user) return null;

      return {
        id: user.sub!,
        email: user.email!,
        name: user.name || `${user.given_name} ${user.family_name}`,
        roles: user.groups || [],
        tenant: user.tenant,
        groups: user.groups
      };
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  async getAccessToken(): Promise<string | null> {
    try {
      const accessToken = await this.oktaAuth.tokenManager.get('accessToken');
      return accessToken?.accessToken || null;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  async handleCallback(): Promise<AuthenticationData> {
    await this.oktaAuth.handleLoginRedirect();
    return await this.refreshToken();
  }

  getOktaAuthClient(): OktaAuth {
    return this.oktaAuth;
  }
}
```

### Expo Mobile Provider
```typescript
// src/repositories/auth/expoAuthProvider.ts
import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';

WebBrowser.maybeCompleteAuthSession();

export class ExpoAuthProvider implements IExpoAuthProvider {
  private discovery: AuthSession.DiscoveryDocument;
  private clientId: string;

  constructor() {
    this.clientId = process.env.EXPO_PUBLIC_OKTA_CLIENT_ID!;
    this.discovery = {
      authorizationEndpoint: `${process.env.EXPO_PUBLIC_OKTA_ISSUER}/v1/authorize`,
      tokenEndpoint: `${process.env.EXPO_PUBLIC_OKTA_ISSUER}/v1/token`,
      revocationEndpoint: `${process.env.EXPO_PUBLIC_OKTA_ISSUER}/v1/revoke`,
      userInfoEndpoint: `${process.env.EXPO_PUBLIC_OKTA_ISSUER}/v1/userinfo`,
    };
  }

  async signIn(): Promise<AuthenticationData> {
    const redirectUri = this.makeRedirectUri();
    const request = new AuthSession.AuthRequest({
      clientId: this.clientId,
      scopes: ['openid', 'profile', 'email', 'offline_access'],
      redirectUri,
      responseType: AuthSession.ResponseType.Code,
      codeChallenge: await AuthSession.AuthRequest.createCodeChallengeAsync(),
      codeChallengeMethod: AuthSession.CodeChallengeMethod.S256,
    });

    const result = await request.promptAsync(this.discovery);

    if (result.type !== 'success') {
      throw new Error('Authentication was cancelled or failed');
    }

    const tokenResult = await AuthSession.exchangeCodeAsync(
      {
        clientId: this.clientId,
        code: result.params.code,
        redirectUri,
        codeVerifier: request.codeVerifier!,
      },
      this.discovery
    );

    return {
      accessToken: tokenResult.accessToken,
      refreshToken: tokenResult.refreshToken,
      idToken: tokenResult.idToken,
      expiresAt: Date.now() + (tokenResult.expiresIn! * 1000),
      tokenType: 'Bearer',
      scope: tokenResult.scope?.split(' ')
    };
  }

  async signOut(): Promise<void> {
    // Clear any stored tokens - implementation depends on your storage strategy
    // The actual logout from Okta would require a web browser session
  }

  async refreshToken(): Promise<AuthenticationData> {
    // Implementation would use stored refresh token
    throw new Error('Refresh token implementation needed');
  }

  async isAuthenticated(): Promise<boolean> {
    // Check stored auth data and expiration
    return false; // Placeholder
  }

  async getCurrentUser(): Promise<AuthenticatedUser | null> {
    const accessToken = await this.getAccessToken();
    if (!accessToken) return null;

    try {
      const response = await fetch(this.discovery.userInfoEndpoint!, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user info');
      }

      const user = await response.json();
      return {
        id: user.sub,
        email: user.email,
        name: user.name || `${user.given_name} ${user.family_name}`,
        roles: user.groups || [],
        tenant: user.tenant,
        groups: user.groups
      };
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  async getAccessToken(): Promise<string | null> {
    // Implementation would retrieve from secure storage
    return null; // Placeholder
  }

  makeRedirectUri(): string {
    return AuthSession.makeRedirectUri({
      scheme: 'com.company.app', // Replace with your app scheme
      path: 'callback',
    });
  }

  getAuthRequestConfig(): any {
    return {
      clientId: this.clientId,
      discovery: this.discovery,
      redirectUri: this.makeRedirectUri(),
    };
  }
}
```

## Usage Examples

### Service Usage in Components
```typescript
// src/hooks/useAuthentication.ts
import { useEffect, useState } from 'react';
import { ServiceFactory } from '@/services/serviceFactory';

export function useAuthentication() {
  const [authState, setAuthState] = useState<AuthenticationState>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
    error: null
  });

  const authService = ServiceFactory.createAuthenticationService();

  useEffect(() => {
    loadAuthState();
  }, []);

  const loadAuthState = async () => {
    const state = await authService.getCurrentAuthState();
    setAuthState(state);
  };

  const signIn = async () => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    const result = await authService.signIn();
    setAuthState(result);
  };

  const signOut = async () => {
    setAuthState(prev => ({ ...prev, isLoading: true }));
    await authService.signOut();
    setAuthState({
      isAuthenticated: false,
      user: null,
      isLoading: false,
      error: null
    });
  };

  const refreshAuth = async () => {
    const success = await authService.refreshAuthIfNeeded();
    if (success) {
      await loadAuthState();
    }
  };

  return {
    ...authState,
    signIn,
    signOut,
    refreshAuth,
    getAccessToken: () => authService.getAccessToken()
  };
}
```

### Secure Storage Hook
```typescript
// src/hooks/useSecureStorage.ts
import { useState, useCallback } from 'react';
import { ServiceFactory } from '@/services/serviceFactory';

export function useSecureStorage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const storageService = ServiceFactory.createSecureStorageService();

  const storeData = useCallback(async <T>(key: string, data: T): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    const result = await storageService.storeSecureData(key, data);

    setIsLoading(false);
    if (!result.success) {
      setError(result.error || 'Storage failed');
      return false;
    }
    return true;
  }, [storageService]);

  const getData = useCallback(async <T>(key: string): Promise<T | null> => {
    setIsLoading(true);
    setError(null);

    const result = await storageService.getSecureData<T>(key);

    setIsLoading(false);
    if (!result.success) {
      setError(result.error || 'Retrieval failed');
      return null;
    }
    return result.data || null;
  }, [storageService]);

  const removeData = useCallback(async (key: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    const result = await storageService.removeSecureData(key);

    setIsLoading(false);
    if (!result.success) {
      setError(result.error || 'Removal failed');
      return false;
    }
    return true;
  }, [storageService]);

  const clearAll = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    const result = await storageService.clearAllData();

    setIsLoading(false);
    if (!result.success) {
      setError(result.error || 'Clear failed');
      return false;
    }
    return true;
  }, [storageService]);

  return {
    storeData,
    getData,
    removeData,
    clearAll,
    isLoading,
    error
  };
}
```

## Implementation Guidelines

### File Structure
```
src/
├── repositories/
│   ├── secureStorage/
│   │   ├── mobileSecureStorageRepository.ts
│   │   ├── webSecureStorageRepository.ts
│   │   └── interfaces.ts
│   └── auth/
│       ├── oktaWebAuthProvider.ts
│       ├── expoAuthProvider.ts
│       └── interfaces.ts
├── services/
│   ├── authenticationService.ts
│   ├── secureStorageService.ts
│   └── serviceFactory.ts
├── hooks/
│   ├── useAuthentication.ts
│   └── useSecureStorage.ts
└── types/
    ├── auth.ts
    └── storage.ts
```

### Environment Configuration
```typescript
// .env.local (development)
EXPO_PUBLIC_OKTA_ISSUER=https://dev-123456.okta.com/oauth2/default
EXPO_PUBLIC_OKTA_CLIENT_ID=your-dev-client-id

// .env.production
EXPO_PUBLIC_OKTA_ISSUER=https://prod-123456.okta.com/oauth2/default
EXPO_PUBLIC_OKTA_CLIENT_ID=your-prod-client-id
```

## Testing Strategy

### Unit Testing with Mocks
```typescript
// src/services/__tests__/authenticationService.test.ts
import { AuthenticationService } from '../authenticationService';
import { IAuthenticationProvider, IAuthStorageRepository } from '@/types';

describe('AuthenticationService', () => {
  let mockAuthProvider: jest.Mocked<IAuthenticationProvider>;
  let mockStorageRepository: jest.Mocked<IAuthStorageRepository>;
  let authService: AuthenticationService;

  beforeEach(() => {
    mockAuthProvider = {
      signIn: jest.fn(),
      signOut: jest.fn(),
      refreshToken: jest.fn(),
      isAuthenticated: jest.fn(),
      getCurrentUser: jest.fn(),
      getAccessToken: jest.fn(),
    };

    mockStorageRepository = {
      setItem: jest.fn(),
      getItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
      setObject: jest.fn(),
      getObject: jest.fn(),
      removeObject: jest.fn(),
      saveAuthData: jest.fn(),
      getAuthData: jest.fn(),
      clearAuthData: jest.fn(),
      saveUserProfile: jest.fn(),
      getUserProfile: jest.fn(),
    };

    authService = new AuthenticationService(mockAuthProvider, mockStorageRepository);
  });

  it('should successfully sign in and store auth data', async () => {
    const mockAuthData = {
      accessToken: 'test-token',
      expiresAt: Date.now() + 3600000,
      tokenType: 'Bearer' as const
    };
    const mockUser = {
      id: '123',
      email: '<EMAIL>',
      name: 'Test User',
      roles: ['user']
    };

    mockAuthProvider.signIn.mockResolvedValue(mockAuthData);
    mockAuthProvider.getCurrentUser.mockResolvedValue(mockUser);

    const result = await authService.signIn();

    expect(result.isAuthenticated).toBe(true);
    expect(result.user).toEqual(mockUser);
    expect(mockStorageRepository.saveAuthData).toHaveBeenCalledWith(mockAuthData);
    expect(mockStorageRepository.saveUserProfile).toHaveBeenCalledWith(mockUser);
  });

  it('should handle sign in failure', async () => {
    mockAuthProvider.signIn.mockRejectedValue(new Error('Network error'));

    const result = await authService.signIn();

    expect(result.isAuthenticated).toBe(false);
    expect(result.error).toBe('Network error');
  });
});
```

### Integration Testing
```typescript
// src/services/__tests__/serviceFactory.integration.test.ts
import { ServiceFactory } from '../serviceFactory';
import { Platform } from 'react-native';

// Mock Platform.OS for testing
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios' // or 'web' for web tests
  }
}));

describe('ServiceFactory Integration', () => {
  afterEach(() => {
    ServiceFactory.reset();
  });

  it('should create mobile services on mobile platform', () => {
    (Platform.OS as any) = 'ios';

    const authService = ServiceFactory.createAuthenticationService();
    const storageService = ServiceFactory.createSecureStorageService();

    expect(authService).toBeDefined();
    expect(storageService).toBeDefined();
    // Additional assertions about the correct implementations being used
  });

  it('should create web services on web platform', () => {
    (Platform.OS as any) = 'web';

    const authService = ServiceFactory.createAuthenticationService();
    const storageService = ServiceFactory.createSecureStorageService();

    expect(authService).toBeDefined();
    expect(storageService).toBeDefined();
    // Additional assertions about the correct implementations being used
  });
});
```

## Migration Plan

### Phase 1: Infrastructure Setup
1. **Create interfaces and domain models**
   - Define all interfaces in `src/types/`
   - Create domain models for auth and storage

2. **Implement repository layer**
   - Mobile secure storage repository
   - Web secure storage repository
   - Authentication provider interfaces

3. **Create service layer**
   - Authentication service with business logic
   - Secure storage service with validation
   - Service factory with dependency injection

### Phase 2: Platform Implementations
1. **Mobile authentication provider**
   - Implement Expo AuthSession integration
   - Handle PKCE flow and token management

2. **Web authentication provider**
   - Implement Okta SDK integration
   - Handle web-specific auth flows

3. **Testing infrastructure**
   - Unit tests for all services
   - Mock implementations for testing
   - Integration tests for factory pattern

### Phase 3: Integration & Migration
1. **Update existing code**
   - Replace direct repository usage with services
   - Update hooks to use new service layer
   - Migrate existing auth flows

2. **Environment configuration**
   - Set up Okta configuration per environment
   - Configure redirect URIs and client IDs

3. **Testing & validation**
   - End-to-end testing on both platforms
   - Performance testing for storage operations
   - Security validation for token handling

## Security Considerations

### Mobile Security
- **Secure Storage**: Uses iOS Keychain and Android Keystore
- **Token Storage**: Access tokens in memory, refresh tokens in secure storage
- **App Transport Security**: Enforce HTTPS for all network requests

### Web Security
- **Session Storage**: Temporary sensitive data auto-clears on tab close
- **Okta Integration**: Leverages Okta's secure session management
- **CORS Configuration**: Proper trusted origins configuration
- **XSS Protection**: Avoid localStorage for sensitive tokens

### General Security
- **Token Rotation**: Implement refresh token rotation
- **Expiration Handling**: Automatic token refresh before expiration
- **Error Handling**: Secure error messages without token exposure
- **Audit Logging**: Log authentication events for security monitoring

## Benefits of This Architecture

### ✅ Advantages
- **Platform Agnostic**: Business logic works across mobile and web
- **Testable**: All components can be tested in isolation
- **Maintainable**: Clear separation of concerns
- **Secure**: Platform-appropriate security implementations
- **Scalable**: Easy to add new platforms or auth providers
- **Consistent**: Unified API across different implementations

### 🔄 Future Extensibility
- **New Platforms**: Easy to add new platform implementations
- **Auth Providers**: Can support multiple auth providers
- **Storage Backends**: Can add cloud storage or other backends
- **Business Logic**: Centralized place for auth and storage rules

## References
- [Okta Setup Documentation](./okta-setup.md)
- [Web Secure Data Strategy](./web-secure-data-okta.md)
- [Clean Architecture Principles](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [Expo AuthSession Documentation](https://docs.expo.dev/guides/authentication/)
- [Okta Auth JS SDK](https://github.com/okta/okta-auth-js)
