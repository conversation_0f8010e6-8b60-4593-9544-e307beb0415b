/**
 * Simple Okta Login Component
 * 
 * A basic implementation following Expo's official documentation pattern for Okta authentication.
 * This component demonstrates the simplest possible Okta integration using expo-auth-session.
 */

import { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri, useAuthRequest, useAutoDiscovery } from 'expo-auth-session';

// Complete auth session for web - required by Expo docs
WebBrowser.maybeCompleteAuthSession();

interface SimpleOktaLoginProps {
  onAuthSuccess?: (tokens: any) => void;
  onAuthError?: (error: string) => void;
}

export default function SimpleOktaLogin({ onAuthSuccess, onAuthError }: SimpleOktaLoginProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [authResult, setAuthResult] = useState<any>(null);

  // Okta configuration - using environment variables
  const oktaDomain = process.env.EXPO_PUBLIC_OKTA_ISSUER || 'https://integrator-5743111.okta.com/oauth2/default';
  const clientId = process.env.EXPO_PUBLIC_OKTA_CLIENT_ID || 'your-client-id-here';

  // Auto-discovery endpoint as recommended by Expo docs
  const discovery = useAutoDiscovery(oktaDomain);

  // Create auth request following Expo's Okta example
  // Using Expo's auth proxy as per user preference
  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId: clientId,
      scopes: ['openid', 'profile'],
      redirectUri: makeRedirectUri({
        useProxy: true,
      }),
    },
    discovery
  );

  // Handle authentication response
  useEffect(() => {
    if (response?.type === 'success') {
      console.log('✅ Auth Success:', response);
      const { code } = response.params;
      setAuthResult({ success: true, code });
      setIsLoading(false);
      
      Alert.alert(
        'Authentication Success!', 
        `Received authorization code: ${code?.substring(0, 20)}...`,
        [{ text: 'OK' }]
      );
      
      onAuthSuccess?.(response);
    } else if (response?.type === 'error') {
      console.error('❌ Auth Error:', response.error);
      const errorMessage = response.error?.message || 'Authentication failed';
      setAuthResult({ success: false, error: errorMessage });
      setIsLoading(false);
      
      Alert.alert('Authentication Error', errorMessage);
      onAuthError?.(errorMessage);
    } else if (response?.type === 'cancel') {
      console.log('🚫 Auth Cancelled');
      setAuthResult({ success: false, error: 'Authentication cancelled' });
      setIsLoading(false);
      
      Alert.alert('Authentication Cancelled', 'You cancelled the login process');
      onAuthError?.('Authentication cancelled');
    }
  }, [response, onAuthSuccess, onAuthError]);

  const handleLogin = async () => {
    if (!request) {
      Alert.alert('Error', 'Authentication request not ready. Please wait and try again.');
      return;
    }

    console.log('🔐 Starting Okta authentication...');
    console.log('🔐 Configuration:', {
      oktaDomain,
      clientId,
      redirectUri: makeRedirectUri({
        useProxy: true,
      }),
    });

    setIsLoading(true);
    setAuthResult(null);
    
    try {
      await promptAsync();
    } catch (error) {
      console.error('🔐 Login error:', error);
      setIsLoading(false);
      Alert.alert('Login Error', 'Failed to start authentication process');
    }
  };

  const getStatusColor = () => {
    if (authResult?.success) return '#10B981'; // green
    if (authResult?.error) return '#EF4444'; // red
    return '#6B7280'; // gray
  };

  const getStatusText = () => {
    if (isLoading) return 'Authenticating...';
    if (authResult?.success) return 'Authentication Successful!';
    if (authResult?.error) return `Error: ${authResult.error}`;
    return 'Ready to authenticate';
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Simple Okta Login</Text>
      <Text style={styles.subtitle}>Following Expo's Official Documentation</Text>
      
      <View style={styles.configSection}>
        <Text style={styles.configTitle}>Configuration:</Text>
        <Text style={styles.configText}>Domain: {oktaDomain}</Text>
        <Text style={styles.configText}>Client ID: {clientId.substring(0, 8)}...</Text>
        <Text style={styles.configText}>
          Redirect: {makeRedirectUri({ useProxy: true })}
        </Text>
      </View>

      <View style={styles.statusSection}>
        <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]} />
        <Text style={[styles.statusText, { color: getStatusColor() }]}>
          {getStatusText()}
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
        onPress={handleLogin}
        disabled={!request || isLoading}
      >
        <Text style={styles.loginButtonText}>
          {isLoading ? 'Authenticating...' : 'Login with Okta'}
        </Text>
      </TouchableOpacity>

      {authResult?.success && (
        <View style={styles.successSection}>
          <Text style={styles.successTitle}>✅ Success!</Text>
          <Text style={styles.successText}>
            Authorization code received. In a real app, you would exchange this for tokens.
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F9FAFB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#111827',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    color: '#6B7280',
  },
  configSection: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  configTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#374151',
  },
  configText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  statusSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
  },
  loginButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 24,
  },
  loginButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  successSection: {
    backgroundColor: '#ECFDF5',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  successTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#065F46',
    marginBottom: 8,
  },
  successText: {
    fontSize: 14,
    color: '#047857',
    lineHeight: 20,
  },
});
