/**
 * Authentication Screen
 * 
 * Provides UI for Okta login using the Clean Architecture authentication system.
 * Shows different states: loading, sign-in form, and error handling.
 */

import React from 'react';
import { StyleSheet, View, Alert } from 'react-native';
import { Image } from 'expo-image';
import { useRouter } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import CustomButton from '@/components/ui/CustomButton';
import { useAuth } from '@/contexts/AuthContext';

export default function AuthScreen() {
  const router = useRouter();
  const {
    isAuthenticated,
    user,
    isLoading,
    error,
    signIn,
    signOut,
  } = useAuth();

  // If already authenticated, redirect to main app
  React.useEffect(() => {
    if (isAuthenticated && user) {
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, user, router]);

  const handleSignIn = async () => {
    try {
      await signIn();
      // Success will be handled by the useEffect that watches isAuthenticated
    } catch (error) {
      Alert.alert('Sign In Failed', 'Please try again or check your connection.');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      Alert.alert('Signed Out', 'You have been successfully signed out.');
    } catch (error) {
      Alert.alert('Sign Out Failed', 'Please try again.');
    }
  };

  const handleClearError = () => {
    // Error clearing is handled automatically by the context
    // when a new sign-in attempt is made
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.content}>
          <Image 
            source={require('@assets/images/logo.svg')} 
            style={styles.logo} 
          />
          <ThemedText type="title" style={styles.title}>
            Learning Coach Community
          </ThemedText>
          <ThemedText style={styles.loadingText}>
            Checking authentication...
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (isAuthenticated && user) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.content}>
          <Image 
            source={require('@assets/images/logo.svg')} 
            style={styles.logo} 
          />
          <ThemedText type="title" style={styles.title}>
            Welcome Back!
          </ThemedText>
          <ThemedText style={styles.subtitle}>
            Hello, {user.name}
          </ThemedText>
          <ThemedText style={styles.email}>
            {user.email}
          </ThemedText>
          
          <View style={styles.buttonContainer}>
            <CustomButton
              variant="primary"
              onPress={() => router.replace('/(tabs)')}
              style={styles.button}
            >
              Continue to App
            </CustomButton>
            
            <CustomButton
              variant="secondary"
              onPress={handleSignOut}
              style={styles.button}
            >
              Sign Out
            </CustomButton>
          </View>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={styles.content}>
        <Image 
          source={require('@assets/images/logo.svg')} 
          style={styles.logo} 
        />
        <ThemedText type="title" style={styles.title}>
          Learning Coach Community
        </ThemedText>
        <ThemedText style={styles.subtitle}>
          Sign in to access your personalized learning experience
        </ThemedText>

        {error && (
          <View style={styles.errorContainer}>
            <ThemedText style={styles.errorText}>
              {error}
            </ThemedText>
            <CustomButton
              variant="secondary"
              onPress={handleClearError}
              style={styles.clearErrorButton}
            >
              Dismiss
            </CustomButton>
          </View>
        )}

        <View style={styles.buttonContainer}>
          <CustomButton
            variant="primary"
            onPress={handleSignIn}
            disabled={isLoading}
            style={styles.button}
          >
            {isLoading ? 'Signing In...' : 'Sign In with Okta'}
          </CustomButton>
        </View>

        <View style={styles.infoContainer}>
          <ThemedText style={styles.infoText}>
            Secure authentication powered by Okta
          </ThemedText>
          <ThemedText style={styles.infoText}>
            Your credentials are never stored on this device
          </ThemedText>
        </View>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  logo: {
    width: 120,
    height: 60,
    marginBottom: 30,
  },
  title: {
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 30,
    opacity: 0.8,
  },
  email: {
    textAlign: 'center',
    marginBottom: 20,
    fontStyle: 'italic',
    opacity: 0.7,
  },
  loadingText: {
    textAlign: 'center',
    opacity: 0.7,
  },
  buttonContainer: {
    width: '100%',
    gap: 15,
    marginBottom: 30,
  },
  button: {
    width: '100%',
  },
  errorContainer: {
    width: '100%',
    backgroundColor: '#ffebee',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#ffcdd2',
  },
  errorText: {
    color: '#c62828',
    textAlign: 'center',
    marginBottom: 10,
  },
  clearErrorButton: {
    alignSelf: 'center',
  },
  infoContainer: {
    alignItems: 'center',
    gap: 5,
  },
  infoText: {
    fontSize: 12,
    opacity: 0.6,
    textAlign: 'center',
  },
});
