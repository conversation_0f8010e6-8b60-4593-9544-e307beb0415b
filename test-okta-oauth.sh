#!/bin/bash

# Test Okta OAuth Configuration
# This script tests the OAuth flow independently of the Expo app

CLIENT_ID="0oauipzrsuO08tffD697"
ISSUER="https://integrator-5743111.okta.com/oauth2/default"
REDIRECT_URI="https://auth.expo.dev/@charlesrmajor/learning-coach-community"

echo "Testing Okta OAuth Configuration..."
echo "Client ID: $CLIENT_ID"
echo "Issuer: $ISSUER"
echo "Redirect URI: $REDIRECT_URI"
echo ""

# Test 1: Check if the authorization endpoint is accessible
echo "1. Testing authorization endpoint..."
AUTH_URL="${ISSUER}/v1/authorize?client_id=${CLIENT_ID}&response_type=code&scope=openid%20profile%20email&redirect_uri=${REDIRECT_URI}&state=test123"
echo "Authorization URL: $AUTH_URL"
echo ""

# Test 2: Check OIDC discovery document
echo "2. Testing OIDC discovery document..."
curl -s "${ISSUER}/.well-known/openid_configuration" | jq '.' || echo "Failed to fetch OIDC configuration"
echo ""

# Test 3: Check if client_id is valid (this will return an error but tells us if the client exists)
echo "3. Testing client validation..."
curl -s -X POST "${ISSUER}/v1/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials&client_id=${CLIENT_ID}" \
  | jq '.' || echo "Client validation test completed"

echo ""
echo "Manual test: Open this URL in your browser:"
echo "$AUTH_URL"
