/**
 * Manage API Configuration Testing Use Case (Development Only)
 *
 * Application-specific business rules for API configuration testing and development.
 * This file should NOT be included in production builds.
 *
 * AI-generated; for use in Api Keys test tab; can probably be removed
 */

import { apiTestingService } from '@/services/__dev__/apiTestingService';
import { apiConfigTestingService } from '@/services/__dev__/apiConfigTestingService';
import { devConfigService, type DevConfigLoadResult } from '@/services/__dev__/devConfigService';

export interface ConfigurationTestResults {
	save: boolean | null;
	retrieve: boolean | null;
	delete: boolean | null;
	exists: boolean | null;
	saveUrl: boolean | null;
	retrieveUrl: boolean | null;
	apiTest: boolean | null;
}

/**
 * Use case for managing API configuration testing (Development Only)
 */
export class ManageApiConfigurationTestingService {
	/**
	 * Load development configuration
	 */
	async loadDevConfiguration(forceReload = false): Promise<DevConfigLoadResult> {
		return devConfigService.loadDevConfiguration(forceReload);
	}

	/**
	 * Test API key retrieval
	 */
	async testApiKeyRetrieval(
		expectedKey: string | null,
	): Promise<{ success: boolean; message: string }> {
		try {
			const result = await apiConfigTestingService.testRetrieveApiKey();
			const isSuccess = result.success && result.retrievedKey === expectedKey;

			return {
				success: isSuccess,
				message: isSuccess
					? `Retrieved API key: ${result.retrievedKey?.substring(0, 10)}...`
					: result.error || 'Retrieved API key does not match stored value',
			};
		} catch (error: any) {
			return {
				success: false,
				message: `Failed to retrieve API key: ${error.message}`,
			};
		}
	}

	/**
	 * Test API base URL retrieval
	 */
	async testApiBaseUrlRetrieval(
		expectedUrl: string | null,
	): Promise<{ success: boolean; message: string }> {
		try {
			const result = await apiConfigTestingService.testRetrieveApiBaseUrl();
			const isSuccess = result.success && result.retrievedUrl === expectedUrl;

			return {
				success: isSuccess,
				message: isSuccess
					? `Retrieved API base URL: ${result.retrievedUrl}`
					: result.error || 'Retrieved API base URL does not match stored value',
			};
		} catch (error: any) {
			return {
				success: false,
				message: `Failed to retrieve API base URL: ${error.message}`,
			};
		}
	}

	/**
	 * Test API key existence
	 */
	async testApiKeyExistence(
		expectedExists: boolean,
	): Promise<{ success: boolean; message: string }> {
		try {
			const result = await apiConfigTestingService.testApiKeyExists();
			const isSuccess = result.success && result.exists === expectedExists;

			return {
				success: isSuccess,
				message: isSuccess
					? `Key existence check passed: ${result.exists ? 'exists' : 'does not exist'}`
					: result.error || `Expected: ${expectedExists}, Got: ${result.exists}`,
			};
		} catch (error: any) {
			return {
				success: false,
				message: `Failed to check key existence: ${error.message}`,
			};
		}
	}

	/**
	 * Test API call functionality
	 */
	async testApiCall(
		apiKey: string,
		apiBaseUrl: string,
	): Promise<{ success: boolean; message: string; response?: string }> {
		if (!apiKey || !apiBaseUrl) {
			return {
				success: false,
				message: 'Both API key and base URL are required',
			};
		}

		try {
			const result = await apiTestingService.testApiCall(apiKey, apiBaseUrl);

			if (result.success) {
				const responseText = `Status: ${result.status}\n\nResponse:\n${JSON.stringify(result.data, null, 2)}`;
				return {
					success: true,
					message: 'API call successful!',
					response: responseText,
				};
			} else {
				const errorText = result.status
					? `Status: ${result.status}\n\nResponse:\n${JSON.stringify(result.data, null, 2)}`
					: `Error: ${result.error}`;
				return {
					success: false,
					message: result.error || 'Unknown error occurred',
					response: errorText,
				};
			}
		} catch (error: any) {
			return {
				success: false,
				message: error.message,
				response: `Error: ${error.message}`,
			};
		}
	}

	/**
	 * Test factory pattern demonstration
	 */
	async testFactoryPattern(
		apiKey: string,
		apiBaseUrl: string,
	): Promise<{ success: boolean; message: string; response?: string }> {
		if (!apiKey || !apiBaseUrl) {
			return {
				success: false,
				message: 'Both API key and base URL are required',
			};
		}

		try {
			const result = await apiTestingService.testFactoryPattern(apiKey, apiBaseUrl);

			if (result.success) {
				const responseText = `API Factory Pattern Demo - Success!\n\nFactory Benefits:\n- Dependency injection ✅\n- Configurable options ✅\n- Consistent interface ✅\n- Centralized client creation ✅\n\nAPI Response (Status: ${result.status}):\n${JSON.stringify(result.data, null, 2)}`;
				return {
					success: true,
					message: 'Factory pattern demonstrated successfully!',
					response: responseText,
				};
			} else {
				const errorText = `API Factory Demo - Error:\nStatus: ${result.status || 'N/A'}\nError: ${result.error}`;
				return {
					success: false,
					message: result.error || 'Unknown error occurred',
					response: errorText,
				};
			}
		} catch (error: any) {
			return {
				success: false,
				message: error.message,
				response: `API Factory Demo - Error:\n${error.message}`,
			};
		}
	}

	/**
	 * Run comprehensive test suite
	 */
	async runTestSuite(
		apiKey: string | null,
		apiBaseUrl: string | null,
	): Promise<{
		results: ConfigurationTestResults;
		apiResponse?: string;
		allPassed: boolean;
		summary: string;
	}> {
		try {
			const testResults = await apiTestingService.runTestSuite(apiKey, apiBaseUrl);

			// Convert to expected format
			const results: ConfigurationTestResults = {
				save: testResults.configTests.save,
				retrieve: testResults.configTests.retrieve,
				delete: null, // Not tested in suite
				exists: testResults.configTests.exists,
				saveUrl: testResults.configTests.saveUrl,
				retrieveUrl: testResults.configTests.retrieveUrl,
				apiTest: testResults.apiTests.basicCall,
			};

			const summary = `Retrieve Key: ${results.retrieve ? '✅' : '❌'}\nRetrieve URL: ${apiBaseUrl ? (results.retrieveUrl ? '✅' : '❌') : 'N/A'}\nExists: ${results.exists ? '✅' : '❌'}\nAPI Test: ${apiBaseUrl ? (results.apiTest ? '✅' : '❌') : 'N/A'}`;

			return {
				results,
				apiResponse: testResults.apiResponse,
				allPassed: testResults.allPassed,
				summary,
			};
		} catch (error: any) {
			throw new Error(`Test suite failed: ${error.message}`);
		}
	}
}

// Export singleton instance for development use
export const manageApiConfigurationTestingService = new ManageApiConfigurationTestingService();
