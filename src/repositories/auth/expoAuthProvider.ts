/**
 * Expo Authentication Provider
 * 
 * Implementation of authentication for mobile platforms using expo-auth-session.
 * Handles OIDC authentication with PKCE flow for secure mobile authentication.
 */

import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import { IExpoAuthProvider } from './interfaces';
import { AuthenticationData, AuthenticatedUser, AuthConfig } from '@/types/auth';

// Complete auth session when returning to app
WebBrowser.maybeCompleteAuthSession();

/**
 * Expo implementation of authentication using expo-auth-session
 */
export class ExpoAuthProvider implements IExpoAuthProvider {
  private discovery: AuthSession.DiscoveryDocument;
  private clientId: string;
  private redirectUri: string;
  private scopes: string[];
  private currentRequest: AuthSession.AuthRequest | null = null;

  constructor(config?: Partial<AuthConfig>) {
    // Use environment variables with config overrides
    this.clientId = config?.clientId || process.env.EXPO_PUBLIC_OKTA_CLIENT_ID!;
    this.scopes = config?.scopes || ['openid', 'profile', 'email', 'offline_access'];

    // Determine redirect URI based on environment and config
    let redirectUri: string;
    if (config?.redirectUri) {
      redirectUri = config.redirectUri;
    } else {
      // Always use Expo auth proxy for consistent behavior
      redirectUri = "https://auth.expo.dev/@charlesrmajor/learning-coach-community";
    }
    this.redirectUri = redirectUri;

    if (!this.clientId) {
      throw new Error('Okta Client ID is required for Expo authentication');
    }

    const issuer = config?.issuer || process.env.EXPO_PUBLIC_OKTA_ISSUER || 'https://integrator-5743111.okta.com/oauth2/default';

    // Log the configuration being used for debugging
    console.log('🔐 [ExpoAuthProvider] Initializing with configuration:');
    console.log('🔐 [ExpoAuthProvider] Redirect URI:', this.redirectUri);
    console.log('🔐 [ExpoAuthProvider] Okta issuer:', issuer);
    console.log('🔐 [ExpoAuthProvider] Client ID:', this.clientId);
    console.log('🔐 [ExpoAuthProvider] Scopes:', this.scopes);
    console.log('🔐 [ExpoAuthProvider] Environment variables:');
    console.log('  - EXPO_PUBLIC_OKTA_ISSUER:', process.env.EXPO_PUBLIC_OKTA_ISSUER);
    console.log('  - EXPO_PUBLIC_OKTA_CLIENT_ID:', process.env.EXPO_PUBLIC_OKTA_CLIENT_ID);
    if (!issuer) {
      throw new Error('Okta Issuer is required for Expo authentication');
    }

    // Set up discovery document for Okta OIDC endpoints
    this.discovery = {
      authorizationEndpoint: `${issuer}/v1/authorize`,
      tokenEndpoint: `${issuer}/v1/token`,
      revocationEndpoint: `${issuer}/v1/revoke`,
      userInfoEndpoint: `${issuer}/v1/userinfo`,
      endSessionEndpoint: `${issuer}/v1/logout`,
    };

    console.log('🔐 [ExpoAuthProvider] Discovery endpoints configured:');
    console.log('  - Authorization:', this.discovery.authorizationEndpoint);
    console.log('  - Token:', this.discovery.tokenEndpoint);
    console.log('  - UserInfo:', this.discovery.userInfoEndpoint);
    console.log('🔐 [ExpoAuthProvider] Initialization complete');
  }



  /**
   * Initiate sign-in process using OIDC with PKCE
   */
  async signIn(): Promise<AuthenticationData> {
    try {
      console.log('🔐 [ExpoAuthProvider] Starting sign-in process');
      console.log('🔐 [ExpoAuthProvider] Configuration:', {
        clientId: this.clientId,
        redirectUri: this.redirectUri,
        scopes: this.scopes,
      });

      // Try using Expo's built-in PKCE generation
      console.log('🔐 [ExpoAuthProvider] Creating AuthRequest with usePKCE: true...');
      const request = new AuthSession.AuthRequest({
        clientId: this.clientId,
        scopes: this.scopes,
        redirectUri: this.redirectUri,
        responseType: AuthSession.ResponseType.Code,
        usePKCE: true,
        prompt: AuthSession.Prompt.SelectAccount,
      });

      // Store the request for later use
      this.currentRequest = request;

      console.log('🔐 [ExpoAuthProvider] AuthRequest created, checking properties...');
      console.log('🔐 [ExpoAuthProvider] Request codeChallenge property:', request.codeChallenge);
      console.log('🔐 [ExpoAuthProvider] Request codeChallengeMethod property:', request.codeChallengeMethod);

      // Try to get the auth request config to see if PKCE is generated there
      console.log('🔐 [ExpoAuthProvider] Getting auth request config...');
      try {
        const config = await request.getAuthRequestConfigAsync();
        console.log('🔐 [ExpoAuthProvider] Auth request config:', {
          codeChallenge: config.codeChallenge,
          codeChallengeMethod: config.codeChallengeMethod,
          usePKCE: config.usePKCE,
        });

        // Check if codeChallenge is now available after getting config
        console.log('🔐 [ExpoAuthProvider] Request properties after getAuthRequestConfigAsync:');
        console.log('🔐 [ExpoAuthProvider] Request codeChallenge:', request.codeChallenge);
        console.log('🔐 [ExpoAuthProvider] Request codeVerifier:', request.codeVerifier);
      } catch (error) {
        console.error('🔐 [ExpoAuthProvider] Failed to get auth request config:', error);
      }

      console.log('🔐 [ExpoAuthProvider] Auth request created:', {
        clientId: request.clientId,
        scopes: request.scopes,
        redirectUri: request.redirectUri,
        responseType: request.responseType,
        codeChallenge: request.codeChallenge,
        codeChallengeMethod: request.codeChallengeMethod,
      });

      console.log('🔐 [ExpoAuthProvider] Discovery endpoint:', this.discovery);
      console.log('🔐 [ExpoAuthProvider] About to prompt for authentication...');

      // Prompt for authentication
      console.log('🔐 [ExpoAuthProvider] Calling request.promptAsync...');
      const result = await request.promptAsync(this.discovery);
      console.log('🔐 [ExpoAuthProvider] promptAsync completed');

      console.log('🔐 [ExpoAuthProvider] Authentication result type:', result.type);

      if (result.type !== 'success') {
        console.error('🔐 [ExpoAuthProvider] Authentication failed:', {
          type: result.type,
        });
        throw new Error(`Authentication failed: ${result.type}`);
      }

      // Type guard to ensure we have a success result
      if (result.type === 'success') {
        console.log('🔐 [ExpoAuthProvider] Success result details:', {
          params: result.params,
          url: result.url,
        });

        if (!result.params.code) {
          console.error('🔐 [ExpoAuthProvider] No authorization code received');
          throw new Error('No authorization code received');
        }
      }

      console.log('🔐 [ExpoAuthProvider] Authorization code received successfully');

      // Exchange code for tokens using the request's code verifier
      if (!request.codeVerifier) {
        console.error('🔐 [ExpoAuthProvider] No code verifier available in request');
        throw new Error('No code verifier available for token exchange');
      }

      console.log('🔐 [ExpoAuthProvider] Code verifier available:', request.codeVerifier.substring(0, 10) + '...');
      return await this.exchangeCodeForTokens(result.params.code, request.codeVerifier);
    } catch (error) {
      console.error('Sign in failed:', error);
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    try {
      // Clear any stored tokens - this would be handled by the storage service
      // For now, we just log the sign out
      console.log('User signed out from Expo auth provider');
      
      // Note: Full logout from Okta would require opening a browser session
      // This is typically handled by clearing local tokens and optionally
      // redirecting to Okta's logout endpoint
    } catch (error) {
      console.error('Sign out failed:', error);
      throw new Error(`Sign out failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Refresh the current authentication token
   */
  async refreshToken(): Promise<AuthenticationData> {
    try {
      // This would typically use a stored refresh token
      // For now, throw an error indicating implementation needed
      throw new Error('Token refresh not implemented - requires stored refresh token');
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw new Error(`Token refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      // This would check stored auth data and expiration
      // For now, return false as a placeholder
      return false;
    } catch (error) {
      console.error('Authentication check failed:', error);
      return false;
    }
  }

  /**
   * Get current authenticated user information
   */
  async getCurrentUser(): Promise<AuthenticatedUser | null> {
    try {
      const accessToken = await this.getAccessToken();
      if (!accessToken) {
        return null;
      }

      // Fetch user info from OIDC userinfo endpoint
      const response = await fetch(this.discovery.userInfoEndpoint!, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user info: ${response.status} ${response.statusText}`);
      }

      const userInfo = await response.json();
      
      return {
        id: userInfo.sub,
        email: userInfo.email,
        name: userInfo.name || `${userInfo.given_name || ''} ${userInfo.family_name || ''}`.trim(),
        roles: userInfo.groups || [],
        tenant: userInfo.tenant,
        groups: userInfo.groups,
        attributes: {
          given_name: userInfo.given_name,
          family_name: userInfo.family_name,
          preferred_username: userInfo.preferred_username,
        },
      };
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  /**
   * Get current access token
   */
  async getAccessToken(): Promise<string | null> {
    try {
      // This would retrieve from secure storage
      // For now, return null as placeholder
      return null;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  /**
   * Get current ID token
   */
  async getIdToken(): Promise<string | null> {
    try {
      // This would retrieve from secure storage
      // For now, return null as placeholder
      return null;
    } catch (error) {
      console.error('Failed to get ID token:', error);
      return null;
    }
  }

  /**
   * Check if current token is expired
   */
  async isTokenExpired(): Promise<boolean> {
    try {
      const expiration = await this.getTokenExpiration();
      if (!expiration) {
        return true;
      }
      
      return Date.now() >= expiration;
    } catch (error) {
      console.error('Failed to check token expiration:', error);
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  async getTokenExpiration(): Promise<number | null> {
    try {
      // This would retrieve from stored auth data
      // For now, return null as placeholder
      return null;
    } catch (error) {
      console.error('Failed to get token expiration:', error);
      return null;
    }
  }

  /**
   * Create redirect URI for the current platform
   */
  makeRedirectUri(): string {
    return AuthSession.makeRedirectUri({
      scheme: process.env.EXPO_PUBLIC_APP_SCHEME || 'com.company.app',
      path: 'callback',
    });
  }

  /**
   * Get authentication request configuration
   */
  getAuthRequestConfig(): any {
    return {
      clientId: this.clientId,
      discovery: this.discovery,
      redirectUri: this.redirectUri,
      scopes: this.scopes,
    };
  }

  /**
   * Create and configure auth request
   */
  async createAuthRequest(config?: Partial<AuthConfig>): Promise<AuthSession.AuthRequest> {
    const mergedConfig = {
      clientId: this.clientId,
      scopes: this.scopes,
      redirectUri: this.redirectUri,
      ...config,
    };

    return new AuthSession.AuthRequest({
      clientId: mergedConfig.clientId,
      scopes: mergedConfig.scopes,
      redirectUri: mergedConfig.redirectUri,
      responseType: AuthSession.ResponseType.Code,
      usePKCE: true,
    });
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code: string, codeVerifier: string): Promise<AuthenticationData> {
    try {
      console.log('🔐 [ExpoAuthProvider] Starting token exchange');
      console.log('🔐 [ExpoAuthProvider] Token exchange parameters:', {
        clientId: this.clientId,
        redirectUri: this.redirectUri,
        codeLength: code.length,
        codeVerifierLength: codeVerifier.length,
      });

      const tokenResult = await AuthSession.exchangeCodeAsync(
        {
          clientId: this.clientId,
          code,
          redirectUri: this.redirectUri,
          extraParams: {
            code_verifier: codeVerifier,
          },
        },
        this.discovery
      );

      console.log('🔐 [ExpoAuthProvider] Token exchange result:', {
        hasAccessToken: !!tokenResult.accessToken,
        hasRefreshToken: !!tokenResult.refreshToken,
        hasIdToken: !!tokenResult.idToken,
        expiresIn: tokenResult.expiresIn,
        tokenType: tokenResult.tokenType,
        scope: tokenResult.scope,
      });

      if (!tokenResult.accessToken) {
        console.error('🔐 [ExpoAuthProvider] No access token received from token exchange');
        throw new Error('No access token received from token exchange');
      }

      console.log('🔐 [ExpoAuthProvider] Token exchange successful');

      return {
        accessToken: tokenResult.accessToken,
        refreshToken: tokenResult.refreshToken || undefined,
        idToken: tokenResult.idToken || undefined,
        expiresAt: Date.now() + (tokenResult.expiresIn! * 1000),
        tokenType: 'Bearer',
        scope: tokenResult.scope?.split(' ') || this.scopes,
      };
    } catch (error) {
      console.error('🔐 [ExpoAuthProvider] Token exchange failed:', error);
      throw new Error(`Token exchange failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Open browser for authentication
   */
  async openAuthSession(authUrl: string): Promise<any> {
    try {
      console.log('🔐 [ExpoAuthProvider] Opening auth session');
      console.log('🔐 [ExpoAuthProvider] Auth URL:', authUrl);
      console.log('🔐 [ExpoAuthProvider] Redirect URI:', this.redirectUri);

      const result = await WebBrowser.openAuthSessionAsync(authUrl, this.redirectUri);

      console.log('🔐 [ExpoAuthProvider] Auth session result:', {
        type: result.type,
        url: result.type === 'success' ? result.url : undefined,
      });

      return result;
    } catch (error) {
      console.error('🔐 [ExpoAuthProvider] Auth session failed:', error);
      throw new Error(`Auth session failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle deep link callback
   */
  async handleDeepLink(url: string): Promise<AuthenticationData | null> {
    try {
      // Parse the URL to extract authorization code
      const urlObj = new URL(url);
      const code = urlObj.searchParams.get('code');
      
      if (!code) {
        console.warn('No authorization code found in deep link');
        return null;
      }

      if (!this.currentRequest?.codeVerifier) {
        throw new Error('No code verifier available for token exchange');
      }

      return await this.exchangeCodeForTokens(code, this.currentRequest.codeVerifier);
    } catch (error) {
      console.error('Deep link handling failed:', error);
      return null;
    }
  }
}
