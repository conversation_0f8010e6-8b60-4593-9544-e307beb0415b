/**
 * API Service
 *
 * Centralized API client using dependency injection for configuration
 * Follows CLEAN architecture principles with injected dependencies
 */

import { type EnvConfig } from '@/config';
import { legacyStorageRepository, StorageKeys } from '@/repositories/legacyStorageAdapter';
import type { IStorageRepository, IApiConfigService, ApiClientOptions, ApiResponse } from '@/types';

/**
 * API Error class
 */
class ApiError extends Error {
	constructor(
		message: string,
		public status?: number,
		public code?: string,
	) {
		super(message);
		this.name = 'ApiError';
	}
}

/**
 * API Client class with dependency injection
 */
class ApiClient {
	private baseUrl: string;
	private timeout: number;
	private retryAttempts: number;
	private config: EnvConfig;
	private storageRepository: IStorageRepository;
	private configService: IApiConfigService;

	constructor(
		config: EnvConfig,
		storageRepository: IStorageRepository = legacyStorageRepository,
		configService: IApiConfigService,
		options: ApiClientOptions = {},
	) {
		this.config = config;
		this.storageRepository = storageRepository;
		this.configService = configService;
		this.baseUrl = options.baseUrl || config.API_URL;
		this.timeout = options.timeout ?? 10000; // Default timeout in milliseconds
		this.retryAttempts = options.retryAttempts ?? 3; // Default retry attempts

		if (config.DEBUG) {
			console.log('🌐 API Client initialized:', {
				baseUrl: this.baseUrl,
				timeout: this.timeout,
				retryAttempts: this.retryAttempts,
			});
		}
	}

	/**
	 * Get authentication headers
	 */
	private async getAuthHeaders(): Promise<Record<string, string>> {
		const token = await this.storageRepository.getItem(StorageKeys.AUTH_DATA);

		if (token) {
			return {
				Authorization: `Bearer ${token}`,
			};
		}

		return {};
	}

	/**
	 * Make HTTP request with retry logic
	 */
	private async makeRequest<T>(
		endpoint: string,
		options: RequestInit = {},
		attempt: number = 1,
	): Promise<ApiResponse<T>> {
		const url = `${this.baseUrl}${endpoint}`;

		try {
			// Get auth headers
			const authHeaders = await this.getAuthHeaders();

			// Prepare request
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), this.timeout);

			const response = await fetch(url, {
				...options,
				headers: {
					'Content-Type': 'application/json',
					...authHeaders,
					...options.headers,
				},
				signal: controller.signal,
			});

			clearTimeout(timeoutId);

			// Handle response
			if (!response.ok) {
				throw new ApiError(
					`HTTP ${response.status}: ${response.statusText}`,
					response.status,
				);
			}

			const data = await response.json();
			return {
				data,
				success: true,
			};
		} catch (error) {
			// Retry logic
			if (attempt < this.retryAttempts && this.shouldRetry(error)) {
				console.warn(
					`API request failed, retrying (${attempt}/${this.retryAttempts}):`,
					error,
				);
				await this.delay(1000 * attempt); // Exponential backoff
				return this.makeRequest<T>(endpoint, options, attempt + 1);
			}

			// Handle different error types
			if (error instanceof ApiError) {
				throw error;
			}

			if (error.name === 'AbortError') {
				throw new ApiError('Request timeout', 408, 'TIMEOUT');
			}

			throw new ApiError(error.message || 'Network error', 0, 'NETWORK_ERROR');
		}
	}

	/**
	 * Determine if request should be retried
	 */
	private shouldRetry(error: any): boolean {
		// Retry on network errors or 5xx server errors
		if (error.name === 'AbortError') return false; // Don't retry timeouts
		if (error instanceof ApiError) {
			return error.status >= 500 || error.status === 0;
		}
		return true; // Retry network errors
	}

	/**
	 * Delay utility for retry logic
	 */
	private delay(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * GET request
	 */
	async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
		let url = endpoint;

		// TODO: test & confirm params handling; ai generated
		if (params) {
			const searchParams = new URLSearchParams();
			Object.entries(params).forEach(([key, value]) => {
				if (value !== undefined && value !== null) {
					searchParams.append(key, String(value));
				}
			});
			url += `?${searchParams.toString()}`;
		}

		return this.makeRequest<T>(url, { method: 'GET' });
	}

	/**
	 * POST request
	 */
	async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
		return this.makeRequest<T>(endpoint, {
			method: 'POST',
			body: data ? JSON.stringify(data) : undefined,
		});
	}

	/**
	 * PUT request
	 */
	async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
		return this.makeRequest<T>(endpoint, {
			method: 'PUT',
			body: data ? JSON.stringify(data) : undefined,
		});
	}

	/**
	 * DELETE request
	 */
	async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
		return this.makeRequest<T>(endpoint, { method: 'DELETE' });
	}

	/**
	 * Upload file
	 */
	async upload<T>(
		endpoint: string,
		file: File | Blob,
		additionalData?: Record<string, any>,
	): Promise<ApiResponse<T>> {
		const formData = new FormData();
		formData.append('file', file);

		if (additionalData) {
			Object.entries(additionalData).forEach(([key, value]) => {
				formData.append(key, String(value));
			});
		}

		const authHeaders = await this.getAuthHeaders();

		return this.makeRequest<T>(endpoint, {
			method: 'POST',
			body: formData,
			headers: {
				// Don't set Content-Type for FormData - let browser set it with boundary
				...authHeaders,
			},
		});
	}
}

// Export the ApiClient class for custom instantiation
export { ApiClient };

// Export error class for error handling
export { ApiError };

// Export types
export type { ApiResponse };
