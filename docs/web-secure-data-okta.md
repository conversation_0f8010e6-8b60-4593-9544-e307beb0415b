# Web Secure Data Strategy with Okta SSO

This document outlines the recommended approach for handling secure data on web when using Okta SSO authentication.

## Current Situation

- **Mobile**: Uses `expo-secure-store` for encrypted storage
- **Web**: Currently has complex encrypted localStorage implementation
- **Authentication**: Okta SSO handles session management via cookies
- **Sensitive Data**: Only needed at session level, no persistence required

## Recommended Web Strategy

### 1. Authentication Data
**Use Okta SDK exclusively** - no manual token storage needed:

```typescript
// ❌ Don't store auth tokens manually
await secureStorageRepository.saveAuthData({ token: 'abc123' });

// ✅ Use Okta's built-in session management
const authClient = new OktaAuth({
  issuer: 'https://your-domain.okta.com/oauth2/default',
  clientId: 'your-client-id',
  redirectUri: window.location.origin + '/login/callback'
});

// Check authentication status
const isAuthenticated = await authClient.isAuthenticated();

// Get tokens when needed
const accessToken = await authClient.getAccessToken();
const idToken = await authClient.getIdToken();
```

### 2. Session-Level Sensitive Data
**Use sessionStorage** for temporary sensitive data:

```typescript
// Automatically cleared when tab/browser closes
sessionStorage.setItem('temp-sensitive-data', JSON.stringify(data));
const data = JSON.parse(sessionStorage.getItem('temp-sensitive-data') || 'null');
```

### 3. User Preferences
**Use localStorage** for non-sensitive persistent data:

```typescript
// Theme, language, UI preferences
localStorage.setItem('user-preferences', JSON.stringify({
  theme: 'dark',
  language: 'en',
  notifications: true
}));
```

## Implementation Changes

### Update SecureStorageService

Modify `src/services/secureStorageService.ts` to handle web differently:

```typescript
export class SecureStorageService {
  async saveAuthData(authData: AuthData): Promise<void> {
    if (Platform.OS === 'web') {
      // On web, auth is handled by Okta - no storage needed
      console.warn('Auth data managed by Okta on web platform');
      return;
    }
    
    // Mobile implementation
    await this.repository.saveAuthData(authData);
  }

  async getAuthData(): Promise<AuthData | null> {
    if (Platform.OS === 'web') {
      // Get auth state from Okta instead
      const authClient = getOktaAuthClient(); // Your Okta client instance
      const isAuthenticated = await authClient.isAuthenticated();
      
      if (isAuthenticated) {
        const accessToken = await authClient.getAccessToken();
        return { token: accessToken };
      }
      return null;
    }
    
    // Mobile implementation
    return await this.repository.getAuthData();
  }
}
```

### Simplified Web Storage Repository

Create a lightweight web storage repository:

```typescript
// src/repositories/secureStorageRepository.web.ts
export class SecureStorageRepository {
  // For session-level sensitive data
  async setItem(key: string, value: string): Promise<void> {
    sessionStorage.setItem(key, value);
  }

  async getItem(key: string): Promise<string | null> {
    return sessionStorage.getItem(key);
  }

  // For persistent preferences
  async setPersistentItem(key: string, value: string): Promise<void> {
    localStorage.setItem(key, value);
  }

  async getPersistentItem(key: string): Promise<string | null> {
    return localStorage.getItem(key);
  }

  // Auth methods - delegate to Okta
  async saveAuthData(authData: AuthData): Promise<void> {
    console.warn('Auth data managed by Okta on web platform');
  }

  async getAuthData(): Promise<AuthData | null> {
    // Implementation depends on your Okta setup
    return null; // Handled by Okta SDK
  }
}
```

## Benefits of This Approach

### ✅ Advantages
- **Simpler**: No encryption complexity or crypto dependencies
- **Secure**: Okta handles auth security, sessionStorage auto-clears
- **Performant**: No encryption overhead
- **Smaller bundle**: No crypto-js library needed
- **Natural**: Leverages browser and Okta's built-in security features

### 🔄 Migration Path
1. **Phase 1**: Update auth methods to use Okta SDK
2. **Phase 2**: Replace encrypted storage with sessionStorage/localStorage
3. **Phase 3**: Remove crypto dependencies and complex web storage

## Security Considerations

### What's Secure
- **Okta Session**: Managed via secure HTTP-only cookies
- **sessionStorage**: Automatically cleared, not accessible cross-origin
- **HTTPS**: All data in transit is encrypted

### What to Avoid
- Storing auth tokens in localStorage (persistent, accessible via JS)
- Storing sensitive data in localStorage without encryption
- Manual session management when Okta handles it

## Implementation Notes

### Okta Integration
```typescript
// Initialize Okta client
const authClient = new OktaAuth({
  issuer: process.env.OKTA_ISSUER,
  clientId: process.env.OKTA_CLIENT_ID,
  redirectUri: `${window.location.origin}/login/callback`,
  scopes: ['openid', 'profile', 'email'],
  pkce: true
});

// Check if user is authenticated
const isAuthenticated = await authClient.isAuthenticated();

// Handle login
if (!isAuthenticated) {
  authClient.signInWithRedirect();
}
```

### Storage Usage Guidelines
```typescript
// ✅ Good: Session-level sensitive data
sessionStorage.setItem('temp-user-context', JSON.stringify(context));

// ✅ Good: Non-sensitive preferences
localStorage.setItem('ui-preferences', JSON.stringify(prefs));

// ❌ Avoid: Auth tokens in localStorage
localStorage.setItem('access-token', token); // Don't do this

// ✅ Good: Get auth from Okta
const token = await authClient.getAccessToken();
```

## Next Steps

1. **Evaluate current Okta integration** - ensure proper SDK usage
2. **Identify non-auth sensitive data** - determine what actually needs storage
3. **Simplify web storage implementation** - remove unnecessary encryption
4. **Update service layer** - modify auth data handling for web platform
5. **Test thoroughly** - ensure auth flows work correctly across platforms

This approach aligns with web security best practices and leverages Okta's robust session management while maintaining API consistency with your mobile implementation.
