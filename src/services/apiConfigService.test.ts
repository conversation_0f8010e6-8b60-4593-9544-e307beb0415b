/**
 * Tests for ApiConfigService
 */

import { apiConfigService } from '@/services/apiConfigService';
import { ServiceFactory } from '@/services/serviceFactory';

// Mock dependencies
jest.mock('@/services/serviceFactory');

const mockServiceFactory = ServiceFactory as jest.Mocked<typeof ServiceFactory>;

describe('ApiConfigService', () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe('loadApiConfig', () => {
		it('should load API configuration from secure storage', async () => {
			mockSecureStorageRepository.getItem
				.mockResolvedValueOnce('test-api-key')
				.mockResolvedValueOnce('https://api.example.com');

			const result = await apiConfigService.loadApiConfig();

			expect(result).toEqual({
				apiKey: 'test-api-key',
				apiBaseUrl: 'https://api.example.com',
			});
		});

		it('should handle missing configuration gracefully', async () => {
			mockSecureStorageRepository.getItem
				.mockResolvedValueOnce(null)
				.mockResolvedValueOnce(null);

			const result = await apiConfigService.loadApiConfig();

			expect(result).toEqual({
				apiKey: null,
				apiBaseUrl: null,
			});
		});
	});

	describe('saveApiKey', () => {
		it('should save API key to secure storage', async () => {
			mockSecureStorageRepository.setItem.mockResolvedValue();

			await apiConfigService.saveApiKey('test-key');

			expect(mockSecureStorageRepository.setItem).toHaveBeenCalledWith(
				'test_api_key',
				'test-key',
			);
		});

		it('should throw error for empty API key', async () => {
			await expect(apiConfigService.saveApiKey('')).rejects.toThrow(
				'API key cannot be empty',
			);
		});

		it('should trim whitespace from API key', async () => {
			mockSecureStorageRepository.setItem.mockResolvedValue();

			await apiConfigService.saveApiKey('  test-key  ');

			expect(mockSecureStorageRepository.setItem).toHaveBeenCalledWith(
				'test_api_key',
				'test-key',
			);
		});
	});

	describe('saveApiBaseUrl', () => {
		it('should save valid API base URL to secure storage', async () => {
			mockSecureStorageRepository.setItem.mockResolvedValue();

			await apiConfigService.saveApiBaseUrl('https://api.example.com');

			expect(mockSecureStorageRepository.setItem).toHaveBeenCalledWith(
				'api_base_url',
				'https://api.example.com',
			);
		});

		it('should throw error for empty URL', async () => {
			await expect(apiConfigService.saveApiBaseUrl('')).rejects.toThrow(
				'API base URL cannot be empty',
			);
		});

		it('should throw error for invalid URL', async () => {
			await expect(apiConfigService.saveApiBaseUrl('not-a-url')).rejects.toThrow(
				'Please enter a valid URL',
			);
		});
	});

	describe('deleteApiConfig', () => {
		it('should delete both API key and base URL', async () => {
			mockSecureStorageRepository.removeItem.mockResolvedValue();

			await apiConfigService.deleteApiConfig();

			expect(mockSecureStorageRepository.removeItem).toHaveBeenCalledWith('test_api_key');
			expect(mockSecureStorageRepository.removeItem).toHaveBeenCalledWith('api_base_url');
		});
	});

	describe('loadDevConfig', () => {
		it('should handle dev config loading gracefully', async () => {
			// Mock __DEV__ to be true
			(global as any).__DEV__ = true;

			const result = await apiConfigService.loadDevConfig();

			expect(result.success).toBe(true);
			expect(result.message).toContain('from dev.config.json');
			expect(result.config).toBeDefined();
		});

		it('should return error in production mode', async () => {
			// Mock __DEV__ to be false
			(global as any).__DEV__ = false;

			const result = await apiConfigService.loadDevConfig();

			expect(result.success).toBe(false);
			expect(result.message).toContain('only available in development mode');
		});
	});
});
