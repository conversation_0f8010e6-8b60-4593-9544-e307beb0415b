/**
 * Okta Expo Authentication Provider
 *
 * Implementation of authentication for Expo React Native using expo-auth-session.
 * This provider uses Expo's AuthSession API which is compatible with the Expo managed workflow.
 */

import * as AuthSession from 'expo-auth-session';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';
import { IOktaAuthProvider } from './interfaces';
import { AuthenticationData, AuthenticatedUser, AuthConfig, AuthResult } from '@/types/auth';

/**
 * Okta Expo implementation of authentication using AuthSession
 */
export class OktaReactNativeAuthProvider implements IOktaAuthProvider {
  private issuer: string;
  private clientId: string;
  private redirectUri: string;
  private scopes: string[];
  private discoveryDocument: AuthSession.DiscoveryDocument | null = null;
  private authRequest: AuthSession.AuthRequest | null = null;

  constructor(config?: Partial<AuthConfig>) {
    // Use environment variables with config overrides
    this.issuer = config?.issuer || process.env.EXPO_PUBLIC_OKTA_ISSUER!;
    this.clientId = config?.clientId || process.env.EXPO_PUBLIC_OKTA_CLIENT_ID!;
    this.scopes = config?.scopes || ['openid', 'profile', 'email', 'offline_access'];

    // Determine redirect URI based on environment and config
    if (config?.redirectUri) {
      this.redirectUri = config.redirectUri;
    } else if (Platform.OS === 'web') {
      // For web, use localhost
      this.redirectUri = `${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:8082'}/callback`;
    } else {
      // For mobile, use Expo auth proxy
      this.redirectUri = "https://auth.expo.dev/@charlesrmajor/learning-coach-community";
    }

    if (!this.issuer) {
      throw new Error('Okta Issuer is required for Expo authentication');
    }

    if (!this.clientId) {
      throw new Error('Okta Client ID is required for Expo authentication');
    }

    this.initializeAuthSession();
  }

  private codeVerifier: string = '';

  private async initializeAuthSession(): Promise<void> {
    try {
      // Load discovery document
      this.discoveryDocument = await AuthSession.fetchDiscoveryAsync(this.issuer);

      // Generate code verifier and challenge
      this.codeVerifier = this.generateCodeVerifier();
      const codeChallenge = await this.generateCodeChallenge(this.codeVerifier);

      // Create auth request
      this.authRequest = new AuthSession.AuthRequest({
        clientId: this.clientId,
        scopes: this.scopes,
        redirectUri: this.redirectUri,
        responseType: AuthSession.ResponseType.Code,
        codeChallenge,
        codeChallengeMethod: AuthSession.CodeChallengeMethod.S256,
        additionalParameters: {},
        extraParams: {},
      });
    } catch (error) {
      console.error('Failed to initialize AuthSession:', error);
      throw new Error(`AuthSession initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private generateCodeVerifier(): string {
    // Generate a random code verifier (43-128 characters)
    const array = new Uint8Array(32);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(array);
    } else {
      // Fallback for environments without crypto.getRandomValues
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return btoa(String.fromCharCode.apply(null, Array.from(array)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  private async generateCodeChallenge(codeVerifier: string): Promise<string> {
    const challenge = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      codeVerifier,
      { encoding: Crypto.CryptoEncoding.BASE64 }
    );
    // Convert BASE64 to BASE64URL
    return challenge.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  }

  private currentTokens: AuthenticationData | null = null;

  /**
   * Initiate sign-in process using Expo AuthSession
   */
  async signIn(): Promise<AuthenticationData> {
    try {
      if (!this.discoveryDocument || !this.authRequest) {
        throw new Error('AuthSession not properly initialized');
      }

      // Check if already authenticated
      if (this.currentTokens && !this.isTokenExpiredSync(this.currentTokens)) {
        return this.currentTokens;
      }

      // Perform authentication
      const result = await AuthSession.promptAsync(this.authRequest, this.discoveryDocument);

      if (result.type === 'success') {
        const { code } = result.params;

        if (!code) {
          throw new Error('No authorization code received');
        }

        // Exchange code for tokens
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: this.clientId,
            code,
            redirectUri: this.redirectUri,
            extraParams: {
              code_verifier: this.codeVerifier,
            },
          },
          this.discoveryDocument
        );

        const authData: AuthenticationData = {
          accessToken: tokenResult.accessToken,
          refreshToken: tokenResult.refreshToken,
          idToken: tokenResult.idToken,
          expiresAt: Date.now() + (tokenResult.expiresIn ? tokenResult.expiresIn * 1000 : 3600 * 1000),
          tokenType: 'Bearer',
          scope: this.scopes,
        };

        this.currentTokens = authData;
        return authData;
      } else if (result.type === 'cancel') {
        throw new Error('Authentication was cancelled by user');
      } else {
        throw new Error(`Authentication failed: ${result.type}`);
      }
    } catch (error) {
      console.error('Sign in failed:', error);
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    try {
      // Clear stored tokens
      this.currentTokens = null;

      // Optionally revoke tokens if we have them
      // For now, just clear local state
      console.log('User signed out successfully');
    } catch (error) {
      console.error('Sign out failed:', error);
      throw new Error(`Sign out failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Refresh the current authentication token
   */
  async refreshToken(): Promise<AuthenticationData> {
    try {
      if (!this.currentTokens?.refreshToken || !this.discoveryDocument) {
        throw new Error('No refresh token available or discovery document not loaded');
      }

      const tokenResult = await AuthSession.refreshAsync(
        {
          clientId: this.clientId,
          refreshToken: this.currentTokens.refreshToken,
        },
        this.discoveryDocument
      );

      const authData: AuthenticationData = {
        accessToken: tokenResult.accessToken,
        refreshToken: tokenResult.refreshToken || this.currentTokens.refreshToken,
        idToken: tokenResult.idToken,
        expiresAt: Date.now() + (tokenResult.expiresIn ? tokenResult.expiresIn * 1000 : 3600 * 1000),
        tokenType: 'Bearer',
        scope: this.scopes,
      };

      this.currentTokens = authData;
      return authData;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw new Error(`Token refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      return this.currentTokens !== null && !this.isTokenExpiredSync(this.currentTokens);
    } catch (error) {
      console.error('Authentication check failed:', error);
      return false;
    }
  }

  private isTokenExpiredSync(tokens: AuthenticationData): boolean {
    return Date.now() >= tokens.expiresAt;
  }

  /**
   * Get current authenticated user information
   */
  async getCurrentUser(): Promise<AuthenticatedUser | null> {
    try {
      if (!this.currentTokens?.idToken) {
        return null;
      }

      // Decode ID token to get user info
      const user = this.decodeIdToken(this.currentTokens.idToken);
      if (!user) {
        return null;
      }

      return {
        id: user.sub!,
        email: user.email!,
        name: user.name || `${user.given_name || ''} ${user.family_name || ''}`.trim(),
        roles: Array.isArray(user.groups) ? user.groups as string[] : typeof user.groups === 'string' ? [user.groups] : [],
        tenant: typeof user.tenant === 'string' ? user.tenant : undefined,
        groups: Array.isArray(user.groups) ? user.groups as string[] : typeof user.groups === 'string' ? [user.groups] : [],
        attributes: {
          given_name: user.given_name,
          family_name: user.family_name,
          preferred_username: user.preferred_username,
          locale: user.locale,
          zoneinfo: user.zoneinfo,
        },
      };
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  private decodeIdToken(idToken: string): any {
    try {
      const payload = idToken.split('.')[1];
      const decoded = JSON.parse(atob(payload));
      return decoded;
    } catch (error) {
      console.error('Failed to decode ID token:', error);
      return null;
    }
  }

  /**
   * Get current access token
   */
  async getAccessToken(): Promise<string | null> {
    try {
      return this.currentTokens?.accessToken || null;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  /**
   * Get current ID token
   */
  async getIdToken(): Promise<string | null> {
    try {
      return this.currentTokens?.idToken || null;
    } catch (error) {
      console.error('Failed to get ID token:', error);
      return null;
    }
  }

  /**
   * Check if current token is expired
   */
  async isTokenExpired(): Promise<boolean> {
    try {
      return this.currentTokens ? this.isTokenExpiredSync(this.currentTokens) : true;
    } catch (error) {
      console.error('Failed to check token expiration:', error);
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  async getTokenExpiration(): Promise<number | null> {
    try {
      return this.currentTokens?.expiresAt || null;
    } catch (error) {
      console.error('Failed to get token expiration:', error);
      return null;
    }
  }

  /**
   * Get the underlying Okta Auth client instance (not applicable for Expo)
   */
  getOktaAuthClient(): any {
    return {
      discoveryDocument: this.discoveryDocument,
      authRequest: this.authRequest,
    };
  }

  /**
   * Handle authentication callback (not needed for Expo AuthSession)
   */
  async handleCallback(): Promise<AuthenticationData> {
    // Not applicable for Expo AuthSession - handled automatically
    if (!this.currentTokens) {
      throw new Error('No authentication data available');
    }
    return this.currentTokens;
  }

  /**
   * Sign in with redirect (not applicable for Expo)
   */
  async signInWithRedirect(originalUri?: string): Promise<void> {
    // Not applicable for Expo - use signIn() instead
    throw new Error('signInWithRedirect not supported in Expo. Use signIn() instead.');
  }

  /**
   * Handle silent authentication
   */
  async silentAuthenticate(): Promise<AuthResult<AuthenticationData>> {
    try {
      const authenticated = await this.isAuthenticated();
      if (authenticated && this.currentTokens) {
        return {
          success: true,
          data: this.currentTokens,
        };
      }

      return {
        success: false,
        error: 'User not authenticated',
      };
    } catch (error) {
      console.error('Silent authentication failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Silent authentication failed',
      };
    }
  }

  /**
   * Get user information
   */
  async getUserInfo(): Promise<any> {
    try {
      return await this.getCurrentUser();
    } catch (error) {
      console.error('Failed to get user info:', error);
      throw new Error(`Failed to get user info: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Revoke tokens
   */
  async revokeTokens(accessToken?: string, refreshToken?: string): Promise<void> {
    try {
      // For now, just clear local tokens
      // In a full implementation, you would call Okta's revoke endpoint
      this.currentTokens = null;
      console.log('Tokens revoked locally');
    } catch (error) {
      console.error('Token revocation failed:', error);
      throw new Error(`Token revocation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
