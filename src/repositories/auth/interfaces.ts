/**
 * Authentication Provider Interfaces
 * 
 * Abstract interfaces for authentication providers.
 * These interfaces define contracts for different authentication implementations.
 */

import { AuthenticationData, AuthenticatedUser, AuthConfig, AuthResult } from '@/types/auth';

/**
 * Base authentication provider interface
 * Defines core authentication operations that all providers must implement
 */
export interface IAuthenticationProvider {
  /**
   * Initiate sign-in process
   * @returns Promise resolving to authentication data
   */
  signIn(): Promise<AuthenticationData>;

  /**
   * Sign out the current user
   */
  signOut(): Promise<void>;

  /**
   * Refresh the current authentication token
   * @returns Promise resolving to new authentication data
   */
  refreshToken(): Promise<AuthenticationData>;

  /**
   * Check if user is currently authenticated
   * @returns Promise resolving to authentication status
   */
  isAuthenticated(): Promise<boolean>;

  /**
   * Get current authenticated user information
   * @returns Promise resolving to user data or null
   */
  getCurrentUser(): Promise<AuthenticatedUser | null>;

  /**
   * Get current access token
   * @returns Promise resolving to access token or null
   */
  getAccessToken(): Promise<string | null>;

  /**
   * Get current ID token (if available)
   * @returns Promise resolving to ID token or null
   */
  getIdToken(): Promise<string | null>;

  /**
   * Check if current token is expired
   * @returns Promise resolving to expiration status
   */
  isTokenExpired(): Promise<boolean>;

  /**
   * Get token expiration time
   * @returns Promise resolving to expiration timestamp or null
   */
  getTokenExpiration(): Promise<number | null>;
}

/**
 * Okta-specific authentication provider interface
 * Extends base provider with Okta-specific functionality
 */
export interface IOktaAuthProvider extends IAuthenticationProvider {
  /**
   * Get the underlying Okta Auth client instance
   * @returns Okta Auth client
   */
  getOktaAuthClient(): any;

  /**
   * Handle authentication callback after redirect
   * @returns Promise resolving to authentication data
   */
  handleCallback(): Promise<AuthenticationData>;

  /**
   * Sign in with redirect (web-specific)
   * @param originalUri Optional URI to redirect to after authentication
   */
  signInWithRedirect(originalUri?: string): Promise<void>;

  /**
   * Handle silent authentication (if supported)
   * @returns Promise resolving to authentication result
   */
  silentAuthenticate(): Promise<AuthResult<AuthenticationData>>;

  /**
   * Get user information from Okta
   * @returns Promise resolving to user information
   */
  getUserInfo(): Promise<any>;

  /**
   * Revoke tokens
   * @param accessToken Optional access token to revoke
   * @param refreshToken Optional refresh token to revoke
   */
  revokeTokens(accessToken?: string, refreshToken?: string): Promise<void>;
}

/**
 * Expo Auth Session provider interface
 * Extends base provider with Expo-specific functionality
 */
export interface IExpoAuthProvider extends IAuthenticationProvider {
  /**
   * Create redirect URI for the current platform
   * @returns Redirect URI string
   */
  makeRedirectUri(): string;

  /**
   * Get authentication request configuration
   * @returns Auth request configuration object
   */
  getAuthRequestConfig(): any;

  /**
   * Create and configure auth request
   * @param config Optional configuration overrides
   * @returns Configured auth request
   */
  createAuthRequest(config?: Partial<AuthConfig>): Promise<any>;

  /**
   * Exchange authorization code for tokens
   * @param code Authorization code
   * @param codeVerifier PKCE code verifier
   * @returns Promise resolving to authentication data
   */
  exchangeCodeForTokens(code: string, codeVerifier: string): Promise<AuthenticationData>;

  /**
   * Open browser for authentication
   * @param authUrl Authentication URL
   * @returns Promise resolving to auth result
   */
  openAuthSession(authUrl: string): Promise<any>;

  /**
   * Handle deep link callback
   * @param url Deep link URL
   * @returns Promise resolving to authentication data or null
   */
  handleDeepLink(url: string): Promise<AuthenticationData | null>;
}

/**
 * Authentication provider factory interface
 * For creating appropriate providers based on platform
 */
export interface IAuthProviderFactory {
  /**
   * Create authentication provider for current platform
   * @param config Authentication configuration
   * @returns Appropriate authentication provider
   */
  createProvider(config: AuthConfig): IAuthenticationProvider;

  /**
   * Create Okta provider (web)
   * @param config Okta configuration
   * @returns Okta authentication provider
   */
  createOktaProvider(config: AuthConfig): IOktaAuthProvider;

  /**
   * Create Expo provider (mobile)
   * @param config Expo authentication configuration
   * @returns Expo authentication provider
   */
  createExpoProvider(config: AuthConfig): IExpoAuthProvider;

  /**
   * Get supported provider types for current platform
   * @returns Array of supported provider type names
   */
  getSupportedProviders(): string[];
}

/**
 * Authentication event listener interface
 */
export interface IAuthEventListener {
  /**
   * Handle authentication events
   * @param event Event name
   * @param data Event data
   */
  onAuthEvent(event: string, data: any): void;
}

/**
 * Token manager interface
 * For advanced token management operations
 */
export interface ITokenManager {
  /**
   * Store tokens securely
   * @param authData Authentication data containing tokens
   */
  storeTokens(authData: AuthenticationData): Promise<void>;

  /**
   * Retrieve stored tokens
   * @returns Promise resolving to authentication data or null
   */
  getTokens(): Promise<AuthenticationData | null>;

  /**
   * Clear stored tokens
   */
  clearTokens(): Promise<void>;

  /**
   * Check if tokens need refresh
   * @param bufferMinutes Minutes before expiration to consider refresh needed
   * @returns Promise resolving to refresh needed status
   */
  needsRefresh(bufferMinutes?: number): Promise<boolean>;

  /**
   * Automatically refresh tokens if needed
   * @returns Promise resolving to refresh result
   */
  autoRefresh(): Promise<AuthResult<AuthenticationData>>;

  /**
   * Add token refresh listener
   * @param listener Callback function for token refresh events
   */
  onTokenRefresh(listener: (authData: AuthenticationData) => void): void;

  /**
   * Remove token refresh listener
   * @param listener Callback function to remove
   */
  offTokenRefresh(listener: (authData: AuthenticationData) => void): void;
}
