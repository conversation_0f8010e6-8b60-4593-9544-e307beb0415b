/**
 * Standalone Okta Test Screen
 * 
 * A completely standalone test of Okta authentication following Expo's official docs.
 * This bypasses all existing auth infrastructure to test the basic flow.
 */

import React, { useEffect, useState } from 'react';
import { StyleSheet, View, ScrollView, Text, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri, useAuthRequest, useAutoDiscovery } from 'expo-auth-session';

// Complete auth session for web - required by Expo docs
WebBrowser.maybeCompleteAuthSession();

export default function OktaTestScreen() {
  const [authResult, setAuthResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Okta configuration
  const oktaDomain = process.env.EXPO_PUBLIC_OKTA_ISSUER || 'https://integrator-5743111.okta.com/oauth2/default';
  const clientId = process.env.EXPO_PUBLIC_OKTA_CLIENT_ID || 'your-client-id-here';

  console.log('🔐 [OktaTest] Configuration:', {
    oktaDomain,
    clientId: clientId.substring(0, 8) + '...',
  });

  // Auto-discovery endpoint as recommended by Expo docs
  const discovery = useAutoDiscovery(oktaDomain);

  // Create auth request following Expo's Okta example
  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId: clientId,
      scopes: ['openid', 'profile'],
      redirectUri: makeRedirectUri({
        useProxy: true,
      }),
    },
    discovery
  );

  // Handle authentication response
  useEffect(() => {
    if (response?.type === 'success') {
      console.log('✅ Auth Success:', response);
      const { code } = response.params;
      setAuthResult({ success: true, code, response });
      setIsLoading(false);
      
      Alert.alert(
        'Authentication Success!', 
        `Received authorization code: ${code?.substring(0, 20)}...`,
        [{ text: 'OK' }]
      );
    } else if (response?.type === 'error') {
      console.error('❌ Auth Error:', response.error);
      const errorMessage = response.error?.message || 'Authentication failed';
      setAuthResult({ success: false, error: errorMessage, response });
      setIsLoading(false);
      
      Alert.alert('Authentication Error', errorMessage);
    } else if (response?.type === 'cancel') {
      console.log('🚫 Auth Cancelled');
      setAuthResult({ success: false, error: 'Authentication cancelled', response });
      setIsLoading(false);
      
      Alert.alert('Authentication Cancelled', 'You cancelled the login process');
    }
  }, [response]);

  const handleLogin = async () => {
    if (!request) {
      Alert.alert('Error', 'Authentication request not ready. Please wait and try again.');
      return;
    }

    console.log('🔐 Starting Okta authentication...');
    console.log('🔐 Request details:', {
      clientId,
      redirectUri: makeRedirectUri({ useProxy: true }),
      discovery: discovery ? 'loaded' : 'loading',
    });

    setIsLoading(true);
    setAuthResult(null);
    
    try {
      await promptAsync();
    } catch (error) {
      console.error('🔐 Login error:', error);
      setIsLoading(false);
      Alert.alert('Login Error', 'Failed to start authentication process');
    }
  };

  const getStatusColor = () => {
    if (authResult?.success) return '#10B981'; // green
    if (authResult?.error) return '#EF4444'; // red
    return '#6B7280'; // gray
  };

  const getStatusText = () => {
    if (isLoading) return 'Authenticating...';
    if (authResult?.success) return 'Authentication Successful!';
    if (authResult?.error) return `Error: ${authResult.error}`;
    return 'Ready to authenticate';
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Okta Authentication Test</Text>
          <Text style={styles.headerSubtitle}>
            Standalone test following Expo's official documentation
          </Text>
        </View>

        <View style={styles.configSection}>
          <Text style={styles.configTitle}>Configuration:</Text>
          <Text style={styles.configText}>Domain: {oktaDomain}</Text>
          <Text style={styles.configText}>Client ID: {clientId.substring(0, 8)}...</Text>
          <Text style={styles.configText}>
            Redirect: {makeRedirectUri({ useProxy: true })}
          </Text>
          <Text style={styles.configText}>
            Discovery: {discovery ? '✅ Loaded' : '⏳ Loading...'}
          </Text>
          <Text style={styles.configText}>
            Request: {request ? '✅ Ready' : '⏳ Preparing...'}
          </Text>
        </View>

        <View style={styles.statusSection}>
          <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]} />
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.loginButton, (!request || isLoading) && styles.loginButtonDisabled]}
          onPress={handleLogin}
          disabled={!request || isLoading}
        >
          <Text style={styles.loginButtonText}>
            {isLoading ? 'Authenticating...' : 'Login with Okta'}
          </Text>
        </TouchableOpacity>

        {authResult && (
          <View style={styles.resultSection}>
            <Text style={styles.resultTitle}>
              {authResult.success ? '✅ Success!' : '❌ Error'}
            </Text>
            <Text style={styles.resultText}>
              {authResult.success 
                ? `Authorization code received: ${authResult.code?.substring(0, 30)}...`
                : `Error: ${authResult.error}`
              }
            </Text>
            {authResult.response && (
              <Text style={styles.debugText}>
                Response type: {authResult.response.type}
              </Text>
            )}
          </View>
        )}

        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>How this works:</Text>
          <Text style={styles.infoText}>
            1. Uses useAutoDiscovery() to get Okta endpoints
          </Text>
          <Text style={styles.infoText}>
            2. Uses useAuthRequest() to create the auth request
          </Text>
          <Text style={styles.infoText}>
            3. Uses Expo's auth proxy for redirect handling
          </Text>
          <Text style={styles.infoText}>
            4. Follows the authorization code flow with PKCE
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  configSection: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  configTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#374151',
  },
  configText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  statusSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
  },
  loginButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 24,
  },
  loginButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  resultSection: {
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  resultText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#6B7280',
    fontFamily: 'monospace',
  },
  infoSection: {
    backgroundColor: '#EFF6FF',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#DBEAFE',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E40AF',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#1E40AF',
    marginBottom: 4,
  },
});
