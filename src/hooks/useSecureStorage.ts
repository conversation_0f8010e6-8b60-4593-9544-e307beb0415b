/**
 * Secure Storage Hook
 * 
 * React hook providing secure storage operations.
 * Integrates with the secure storage service to provide a clean API for components.
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { ServiceFactory } from '@/services/serviceFactory';
import { SecureStorageService } from '@/services/secureStorageService';
import { 
  StorageResult, 
  StorageInfo, 
  StorageEvent, 
  StorageEventData 
} from '@/types/storage';

/**
 * Secure storage hook return type
 */
export interface UseSecureStorageReturn {
  // State
  isLoading: boolean;
  error: string | null;
  
  // Storage operations
  storeData: <T>(key: string, data: T) => Promise<boolean>;
  getData: <T>(key: string) => Promise<T | null>;
  removeData: (key: string) => Promise<boolean>;
  hasData: (key: string) => Promise<boolean>;
  clearAll: () => Promise<boolean>;
  
  // Batch operations
  storeMultiple: <T>(items: Array<{ key: string; data: T }>) => Promise<boolean>;
  getMultiple: <T>(keys: string[]) => Promise<Map<string, T | null>>;
  
  // Storage info
  getStorageInfo: () => Promise<StorageInfo | null>;
  
  // Utility
  clearError: () => void;
}

/**
 * Hook options
 */
export interface UseSecureStorageOptions {
  /** Whether to log storage operations for debugging */
  enableLogging?: boolean;
  /** Whether to emit events for storage operations */
  enableEvents?: boolean;
}

/**
 * Secure storage hook providing storage operations
 */
export function useSecureStorage(options: UseSecureStorageOptions = {}): UseSecureStorageReturn {
  const { enableLogging = false, enableEvents = true } = options;

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Service reference
  const storageServiceRef = useRef<SecureStorageService | null>(null);

  // Get storage service instance
  const getStorageService = useCallback((): SecureStorageService => {
    if (!storageServiceRef.current) {
      storageServiceRef.current = ServiceFactory.createSecureStorageService();
    }
    return storageServiceRef.current;
  }, []);

  // Helper to handle operation results
  const handleResult = useCallback(<T>(result: StorageResult<T>): T | null => {
    if (result.success) {
      setError(null);
      return result.data || null;
    } else {
      setError(result.error || 'Operation failed');
      if (enableLogging) {
        console.error('Storage operation failed:', result.error);
      }
      return null;
    }
  }, [enableLogging]);

  // Store data
  const storeData = useCallback(async <T>(key: string, data: T): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const storageService = getStorageService();
      const result = await storageService.storeSecureData(key, data);
      
      if (enableLogging && result.success) {
        console.log(`Stored data for key: ${key}`);
      }
      
      return handleResult(result) !== null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Store operation failed';
      setError(errorMessage);
      if (enableLogging) {
        console.error('Store data error:', error);
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [getStorageService, handleResult, enableLogging]);

  // Get data
  const getData = useCallback(async <T>(key: string): Promise<T | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const storageService = getStorageService();
      const result = await storageService.getSecureData<T>(key);
      
      if (enableLogging) {
        console.log(`Retrieved data for key: ${key}`, result.success ? 'success' : 'failed');
      }
      
      return handleResult(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Get operation failed';
      setError(errorMessage);
      if (enableLogging) {
        console.error('Get data error:', error);
      }
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [getStorageService, handleResult, enableLogging]);

  // Remove data
  const removeData = useCallback(async (key: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const storageService = getStorageService();
      const result = await storageService.removeSecureData(key);
      
      if (enableLogging && result.success) {
        console.log(`Removed data for key: ${key}`);
      }
      
      return handleResult(result) !== null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Remove operation failed';
      setError(errorMessage);
      if (enableLogging) {
        console.error('Remove data error:', error);
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [getStorageService, handleResult, enableLogging]);

  // Check if data exists
  const hasData = useCallback(async (key: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const storageService = getStorageService();
      const result = await storageService.hasData(key);
      
      if (enableLogging) {
        console.log(`Checked existence for key: ${key}`, result.data ? 'exists' : 'not found');
      }
      
      const exists = handleResult(result);
      return exists === true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Existence check failed';
      setError(errorMessage);
      if (enableLogging) {
        console.error('Has data error:', error);
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [getStorageService, handleResult, enableLogging]);

  // Clear all data
  const clearAll = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const storageService = getStorageService();
      const result = await storageService.clearAllData();
      
      if (enableLogging && result.success) {
        console.log('Cleared all storage data');
      }
      
      return handleResult(result) !== null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Clear operation failed';
      setError(errorMessage);
      if (enableLogging) {
        console.error('Clear all error:', error);
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [getStorageService, handleResult, enableLogging]);

  // Store multiple items
  const storeMultiple = useCallback(async <T>(items: Array<{ key: string; data: T }>): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const storageService = getStorageService();
      const result = await storageService.storeMultiple(items);
      
      if (enableLogging) {
        console.log(`Stored ${items.length} items`, result.success ? 'success' : 'partial/failed');
      }
      
      if (!result.success) {
        setError(result.error || 'Some storage operations failed');
      }
      
      return result.success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Batch store operation failed';
      setError(errorMessage);
      if (enableLogging) {
        console.error('Store multiple error:', error);
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [getStorageService, enableLogging]);

  // Get multiple items
  const getMultiple = useCallback(async <T>(keys: string[]): Promise<Map<string, T | null>> => {
    setIsLoading(true);
    setError(null);

    try {
      const storageService = getStorageService();
      const result = await storageService.getMultiple<T>(keys);
      
      if (enableLogging) {
        console.log(`Retrieved ${keys.length} items`, result.success ? 'success' : 'failed');
      }
      
      const data = handleResult(result);
      return data || new Map();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Batch get operation failed';
      setError(errorMessage);
      if (enableLogging) {
        console.error('Get multiple error:', error);
      }
      return new Map();
    } finally {
      setIsLoading(false);
    }
  }, [getStorageService, handleResult, enableLogging]);

  // Get storage info
  const getStorageInfo = useCallback(async (): Promise<StorageInfo | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const storageService = getStorageService();
      const result = await storageService.getStorageInfo();
      
      if (enableLogging) {
        console.log('Retrieved storage info:', result.success ? 'success' : 'failed');
      }
      
      return handleResult(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Get storage info failed';
      setError(errorMessage);
      if (enableLogging) {
        console.error('Get storage info error:', error);
      }
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [getStorageService, handleResult, enableLogging]);

  // Clear error
  const clearError = useCallback((): void => {
    setError(null);
  }, []);

  // Set up event listeners
  useEffect(() => {
    if (!enableEvents) return;

    const storageService = getStorageService();

    const handleStorageEvent = (data: StorageEventData): void => {
      if (enableLogging) {
        console.log('Storage event:', data.event, data);
      }
      
      // Handle specific events if needed
      switch (data.event) {
        case StorageEvent.STORAGE_ERROR:
          if (data.error) {
            setError(data.error);
          }
          break;
      }
    };

    // Add event listeners for all storage events
    Object.values(StorageEvent).forEach(event => {
      storageService.addEventListener(event, handleStorageEvent);
    });

    return () => {
      // Remove event listeners
      Object.values(StorageEvent).forEach(event => {
        storageService.removeEventListener(event, handleStorageEvent);
      });
    };
  }, [getStorageService, enableEvents, enableLogging]);

  return {
    // State
    isLoading,
    error,
    
    // Storage operations
    storeData,
    getData,
    removeData,
    hasData,
    clearAll,
    
    // Batch operations
    storeMultiple,
    getMultiple,
    
    // Storage info
    getStorageInfo,
    
    // Utility
    clearError,
  };
}
