/**
 * Simple Okta Demo Screen
 * 
 * A basic demo screen that showcases the simple Okta login flow.
 * This is a standalone demo following Expo's official documentation pattern.
 */

import React from 'react';
import { StyleSheet, View, ScrollView, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { makeRedirectUri } from 'expo-auth-session';
import SimpleOktaLogin from '@/components/auth/SimpleOktaLogin';

export default function SimpleOktaDemoScreen() {
  const router = useRouter();

  const handleAuthSuccess = (response: any) => {
    console.log('🎉 Demo: Authentication successful!', response);
    // In a real app, you would handle the successful authentication here
    // For example, navigate to the main app or store tokens
  };

  const handleAuthError = (error: string) => {
    console.error('❌ Demo: Authentication error:', error);
    // In a real app, you would handle authentication errors here
    // For example, show user-friendly error messages or retry logic
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Simple Okta Demo</Text>
          <Text style={styles.headerSubtitle}>
            Testing basic Okta authentication with Expo
          </Text>
        </View>

        <View style={styles.demoSection}>
          <SimpleOktaLogin 
            onAuthSuccess={handleAuthSuccess}
            onAuthError={handleAuthError}
          />
        </View>

        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>About This Demo</Text>
          <Text style={styles.infoText}>
            This demo implements the exact pattern from Expo's official documentation for Okta authentication:
          </Text>
          <Text style={styles.bulletPoint}>• Uses useAutoDiscovery() for endpoint discovery</Text>
          <Text style={styles.bulletPoint}>• Uses useAuthRequest() hook for authentication</Text>
          <Text style={styles.bulletPoint}>• Follows the authorization code flow with PKCE</Text>
          <Text style={styles.bulletPoint}>• Uses the native redirect URI pattern</Text>
          
          <Text style={styles.infoText} style={[styles.infoText, { marginTop: 16 }]}>
            Configuration:
          </Text>
          <Text style={styles.bulletPoint}>• Okta Domain: {process.env.EXPO_PUBLIC_OKTA_ISSUER}</Text>
          <Text style={styles.bulletPoint}>• Client ID: {process.env.EXPO_PUBLIC_OKTA_CLIENT_ID?.substring(0, 8)}...</Text>
          <Text style={styles.bulletPoint}>• Redirect URI: {makeRedirectUri({ useProxy: true })}</Text>
        </View>

        <View style={styles.notesSection}>
          <Text style={styles.notesTitle}>Setup Notes</Text>
          <Text style={styles.notesText}>
            1. Make sure your Okta application is configured as a Native Application
          </Text>
          <Text style={styles.notesText}>
            2. Add the redirect URI "https://auth.expo.dev/@charlesrmajor/learning-coach-community" to your Okta app
          </Text>
          <Text style={styles.notesText}>
            3. Enable the "openid" and "profile" scopes in your Okta application
          </Text>
          <Text style={styles.notesText}>
            4. Update the .env.local file with your actual Client ID
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  demoSection: {
    marginBottom: 32,
  },
  infoSection: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 8,
  },
  bulletPoint: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginLeft: 8,
    marginBottom: 4,
  },
  notesSection: {
    backgroundColor: '#FEF3C7',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FCD34D',
  },
  notesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400E',
    marginBottom: 12,
  },
  notesText: {
    fontSize: 14,
    color: '#92400E',
    lineHeight: 20,
    marginBottom: 8,
  },
});
