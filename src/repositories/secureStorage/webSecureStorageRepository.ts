/**
 * Web Secure Storage Repository
 * 
 * Implementation of secure storage for web platforms using sessionStorage and localStorage.
 * Uses sessionStorage for sensitive data (auto-clears on tab close) and localStorage for preferences.
 */

import { IAuthStorageRepository, ISessionStorageRepository, IPreferencesRepository } from '../interfaces';
import { AuthenticationData, AuthenticatedUser } from '@/types/auth';
import { StorageInfo, StorageKeys } from '@/types/storage';

/**
 * Web implementation of secure storage using browser storage APIs
 */
export class WebSecureStorageRepository implements IAuthStorageRepository {
  private readonly keyPrefix: string = 'lcc_';

  constructor() {
    // Check if storage APIs are available
    if (typeof window === 'undefined') {
      console.warn('Web storage is not available in this environment');
    }
  }

  /**
   * Store a string value in sessionStorage (for sensitive data)
   */
  async setItem(key: string, value: string): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        throw new Error('SessionStorage is not available');
      }
      
      const prefixedKey = this.getPrefixedKey(key);
      sessionStorage.setItem(prefixedKey, value);
    } catch (error) {
      console.error(`Failed to store item with key ${key}:`, error);
      throw new Error(`Storage operation failed: ${error}`);
    }
  }

  /**
   * Retrieve a string value from sessionStorage
   */
  async getItem(key: string): Promise<string | null> {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        console.warn('SessionStorage is not available');
        return null;
      }
      
      const prefixedKey = this.getPrefixedKey(key);
      return sessionStorage.getItem(prefixedKey);
    } catch (error) {
      console.error(`Failed to retrieve item with key ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove a stored item from sessionStorage
   */
  async removeItem(key: string): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        console.warn('SessionStorage is not available');
        return;
      }
      
      const prefixedKey = this.getPrefixedKey(key);
      sessionStorage.removeItem(prefixedKey);
    } catch (error) {
      console.error(`Failed to remove item with key ${key}:`, error);
      throw new Error(`Removal operation failed: ${error}`);
    }
  }

  /**
   * Clear all stored items from sessionStorage
   */
  async clear(): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        console.warn('SessionStorage is not available');
        return;
      }
      
      // Clear only our prefixed keys
      const keys = this.getAllStorageKeys();
      keys.forEach(key => {
        if (key.startsWith(this.keyPrefix)) {
          sessionStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Failed to clear storage:', error);
      throw new Error(`Clear operation failed: ${error}`);
    }
  }

  /**
   * Check if a key exists in sessionStorage
   */
  async exists(key: string): Promise<boolean> {
    try {
      const value = await this.getItem(key);
      return value !== null;
    } catch (error) {
      console.error(`Failed to check existence of key ${key}:`, error);
      return false;
    }
  }

  /**
   * Store a typed object in sessionStorage
   */
  async setObject<T>(key: string, value: T): Promise<void> {
    try {
      const jsonString = JSON.stringify(value);
      await this.setItem(key, jsonString);
    } catch (error) {
      console.error(`Failed to store object with key ${key}:`, error);
      throw new Error(`Object storage operation failed: ${error}`);
    }
  }

  /**
   * Retrieve a typed object from sessionStorage
   */
  async getObject<T>(key: string): Promise<T | null> {
    try {
      const jsonString = await this.getItem(key);
      if (jsonString === null) {
        return null;
      }
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error(`Failed to retrieve object with key ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove a stored object from sessionStorage
   */
  async removeObject(key: string): Promise<void> {
    await this.removeItem(key);
  }

  /**
   * Get storage information and statistics
   */
  async getStorageInfo(): Promise<StorageInfo> {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        return {
          itemCount: 0,
          totalSizeBytes: 0,
          provider: 'web-sessionStorage',
          isAvailable: false,
        };
      }

      const keys = this.getAllStorageKeys().filter(key => key.startsWith(this.keyPrefix));
      let totalSize = 0;

      // Calculate approximate size
      keys.forEach(key => {
        const value = sessionStorage.getItem(key);
        if (value) {
          totalSize += new Blob([value]).size;
        }
      });

      return {
        itemCount: keys.length,
        totalSizeBytes: totalSize,
        provider: 'web-sessionStorage',
        isAvailable: true,
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        itemCount: 0,
        totalSizeBytes: 0,
        provider: 'web-sessionStorage',
        isAvailable: false,
      };
    }
  }

  /**
   * Store authentication data (minimal session data only)
   * Note: On web, actual tokens are managed by Okta SDK
   */
  async saveAuthData(authData: AuthenticationData): Promise<void> {
    try {
      // Store minimal session data, not the actual tokens (Okta manages those)
      const sessionData = {
        expiresAt: authData.expiresAt,
        tokenType: authData.tokenType,
        scope: authData.scope,
        // Don't store actual tokens in sessionStorage for security
      };
      await this.setObject(StorageKeys.AUTH_DATA, sessionData);
    } catch (error) {
      throw new Error(`Failed to save authentication data: ${error}`);
    }
  }

  /**
   * Retrieve stored authentication data
   * Note: This returns session metadata only, actual tokens come from Okta SDK
   */
  async getAuthData(): Promise<AuthenticationData | null> {
    try {
      const sessionData = await this.getObject<Partial<AuthenticationData>>(StorageKeys.AUTH_DATA);
      if (!sessionData) {
        return null;
      }

      // Return partial data - actual tokens will be provided by Okta SDK
      return {
        accessToken: '', // Will be filled by Okta SDK
        expiresAt: sessionData.expiresAt || 0,
        tokenType: sessionData.tokenType || 'Bearer',
        scope: sessionData.scope,
      };
    } catch (error) {
      console.error('Failed to retrieve authentication data:', error);
      return null;
    }
  }

  /**
   * Clear all authentication data
   */
  async clearAuthData(): Promise<void> {
    try {
      await Promise.all([
        this.removeItem(StorageKeys.AUTH_DATA),
        this.removeItem(StorageKeys.USER_PROFILE),
      ]);
    } catch (error) {
      throw new Error(`Failed to clear authentication data: ${error}`);
    }
  }

  /**
   * Store user profile information
   */
  async saveUserProfile(user: AuthenticatedUser): Promise<void> {
    try {
      await this.setObject(StorageKeys.USER_PROFILE, user);
    } catch (error) {
      throw new Error(`Failed to save user profile: ${error}`);
    }
  }

  /**
   * Retrieve stored user profile
   */
  async getUserProfile(): Promise<AuthenticatedUser | null> {
    try {
      return await this.getObject<AuthenticatedUser>(StorageKeys.USER_PROFILE);
    } catch (error) {
      console.error('Failed to retrieve user profile:', error);
      return null;
    }
  }

  /**
   * Check if authentication data exists and is valid
   */
  async hasValidAuthData(): Promise<boolean> {
    try {
      const authData = await this.getAuthData();
      if (!authData) {
        return false;
      }

      // Check if token is not expired
      const now = Date.now();
      return authData.expiresAt > now;
    } catch (error) {
      console.error('Failed to check auth data validity:', error);
      return false;
    }
  }

  /**
   * Get all storage keys from sessionStorage
   */
  private getAllStorageKeys(): string[] {
    if (typeof window === 'undefined' || !window.sessionStorage) {
      return [];
    }

    const keys: string[] = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key) {
        keys.push(key);
      }
    }
    return keys;
  }

  /**
   * Add prefix to storage key to avoid conflicts
   */
  private getPrefixedKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }
}

/**
 * Web session storage implementation for temporary data
 */
export class WebSessionStorageRepository implements ISessionStorageRepository {
  private readonly keyPrefix: string = 'lcc_session_';

  /**
   * Store a session item
   */
  async setSessionItem(key: string, value: string): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        throw new Error('SessionStorage is not available');
      }

      const prefixedKey = this.getPrefixedKey(key);
      sessionStorage.setItem(prefixedKey, value);
    } catch (error) {
      console.error(`Failed to store session item with key ${key}:`, error);
      throw new Error(`Session storage operation failed: ${error}`);
    }
  }

  /**
   * Retrieve a session item
   */
  async getSessionItem(key: string): Promise<string | null> {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        return null;
      }

      const prefixedKey = this.getPrefixedKey(key);
      return sessionStorage.getItem(prefixedKey);
    } catch (error) {
      console.error(`Failed to retrieve session item with key ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove a session item
   */
  async removeSessionItem(key: string): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        return;
      }

      const prefixedKey = this.getPrefixedKey(key);
      sessionStorage.removeItem(prefixedKey);
    } catch (error) {
      console.error(`Failed to remove session item with key ${key}:`, error);
      throw new Error(`Session removal operation failed: ${error}`);
    }
  }

  /**
   * Clear all session data
   */
  async clearSession(): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.sessionStorage) {
        return;
      }

      // Clear only our prefixed session keys
      const keys: string[] = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && key.startsWith(this.keyPrefix)) {
          keys.push(key);
        }
      }

      keys.forEach(key => sessionStorage.removeItem(key));
    } catch (error) {
      console.error('Failed to clear session storage:', error);
      throw new Error(`Session clear operation failed: ${error}`);
    }
  }

  /**
   * Store a typed object in session
   */
  async setSessionObject<T>(key: string, value: T): Promise<void> {
    try {
      const jsonString = JSON.stringify(value);
      await this.setSessionItem(key, jsonString);
    } catch (error) {
      console.error(`Failed to store session object with key ${key}:`, error);
      throw new Error(`Session object storage operation failed: ${error}`);
    }
  }

  /**
   * Retrieve a typed object from session
   */
  async getSessionObject<T>(key: string): Promise<T | null> {
    try {
      const jsonString = await this.getSessionItem(key);
      if (jsonString === null) {
        return null;
      }
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error(`Failed to retrieve session object with key ${key}:`, error);
      return null;
    }
  }

  private getPrefixedKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }
}

/**
 * Web preferences repository for non-sensitive, persistent data
 */
export class WebPreferencesRepository implements IPreferencesRepository {
  private readonly keyPrefix: string = 'lcc_pref_';

  /**
   * Store a preference value in localStorage
   */
  async setPreference(key: string, value: string): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        throw new Error('LocalStorage is not available');
      }

      const prefixedKey = this.getPrefixedKey(key);
      localStorage.setItem(prefixedKey, value);
    } catch (error) {
      console.error(`Failed to store preference with key ${key}:`, error);
      throw new Error(`Preference storage operation failed: ${error}`);
    }
  }

  /**
   * Retrieve a preference value from localStorage
   */
  async getPreference(key: string): Promise<string | null> {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return null;
      }

      const prefixedKey = this.getPrefixedKey(key);
      return localStorage.getItem(prefixedKey);
    } catch (error) {
      console.error(`Failed to retrieve preference with key ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove a preference from localStorage
   */
  async removePreference(key: string): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return;
      }

      const prefixedKey = this.getPrefixedKey(key);
      localStorage.removeItem(prefixedKey);
    } catch (error) {
      console.error(`Failed to remove preference with key ${key}:`, error);
      throw new Error(`Preference removal operation failed: ${error}`);
    }
  }

  /**
   * Clear all preferences from localStorage
   */
  async clearPreferences(): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return;
      }

      // Clear only our prefixed preference keys
      const keys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.keyPrefix)) {
          keys.push(key);
        }
      }

      keys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.error('Failed to clear preferences:', error);
      throw new Error(`Preferences clear operation failed: ${error}`);
    }
  }

  /**
   * Store a typed preference object in localStorage
   */
  async setPreferenceObject<T>(key: string, value: T): Promise<void> {
    try {
      const jsonString = JSON.stringify(value);
      await this.setPreference(key, jsonString);
    } catch (error) {
      console.error(`Failed to store preference object with key ${key}:`, error);
      throw new Error(`Preference object storage operation failed: ${error}`);
    }
  }

  /**
   * Retrieve a typed preference object from localStorage
   */
  async getPreferenceObject<T>(key: string): Promise<T | null> {
    try {
      const jsonString = await this.getPreference(key);
      if (jsonString === null) {
        return null;
      }
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error(`Failed to retrieve preference object with key ${key}:`, error);
      return null;
    }
  }

  private getPrefixedKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }
}
