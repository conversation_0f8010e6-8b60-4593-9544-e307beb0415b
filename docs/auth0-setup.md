# Auth0 Application Setup Guide

**Status**: Ready for Configuration  
**Date**: 2025-08-20  
**For**: Learning Coach Community App with Expo Auth Proxy

## 🎯 Overview

This guide walks you through setting up your Auth0 application to work with the Clean Architecture authentication system using Expo's auth proxy.

## 📋 Prerequisites

- Auth0 Account (free at https://auth0.com)
- Admin access to your Auth0 tenant
- The Learning Coach Community app configured with Clean Architecture

## 🔧 Step-by-Step Configuration

### Step 1: Create New Application

1. **Log into Auth0 Dashboard**
   - Go to your Auth0 tenant (e.g., `https://manage.auth0.com`)
   - Sign in with your admin credentials

2. **Navigate to Applications**
   - Click **Applications** in the left sidebar
   - Click **Create Application**

3. **Choose Application Type**
   - **Name**: `Learning Coach Community`
   - **Application Type**: Select **Single Page Web Applications**
   - Click **Create**

### Step 2: Configure Application Settings

#### Basic Information
- **Name**: Learning Coach Community
- **Description**: Learning Coach Community mobile and web app
- **Application Logo**: Upload your app logo (optional)

#### Application URIs
**Allowed Callback URLs** (add all of these, separated by commas):
```
https://auth.expo.dev/@charlesrmajor/learning-coach-community,
http://localhost:8081/callback,
http://localhost:8082/callback
```

**Allowed Logout URLs**:
```
https://auth.expo.dev/@charlesrmajor/learning-coach-community,
http://localhost:8081,
http://localhost:8082
```

**Allowed Web Origins**:
```
http://localhost:8081,
http://localhost:8082
```

**Allowed Origins (CORS)**:
```
http://localhost:8081,
http://localhost:8082,
https://auth.expo.dev
```

#### Advanced Settings

**Grant Types** (ensure these are enabled):
- ✅ **Authorization Code**
- ✅ **Refresh Token**
- ✅ **Implicit**

**Token Endpoint Authentication Method**:
- Select **None** (for PKCE)

### Step 3: API Configuration

1. **Navigate to APIs** in the Auth0 dashboard
2. **Select "Auth0 Management API"** (or create a custom API if needed)
3. **Note the API Identifier** (you may need this later)

### Step 4: Get Configuration Values

From your Auth0 application settings, note these values:

1. **Domain**: Found in the application settings (e.g., `dev-sc6nc1ye5cbwaxvi.us.auth0.com`)
2. **Client ID**: Found in the application settings
3. **Client Secret**: Not needed for SPA with PKCE

## 📝 Environment Configuration

Update your `.env.local` file:

```bash
# Auth0 Configuration
EXPO_PUBLIC_OKTA_ISSUER=https://dev-sc6nc1ye5cbwaxvi.us.auth0.com
EXPO_PUBLIC_OKTA_CLIENT_ID=your-auth0-client-id-here
EXPO_PUBLIC_APP_SCHEME=learningcoachcommunity
```

**Important Notes:**
- Use your actual Auth0 domain (without `/oauth2/default`)
- Replace `your-auth0-client-id-here` with your actual Client ID
- The variable names still use `OKTA_` prefix for compatibility with existing code

## 🧪 Testing the Configuration

### Test Authentication Flow

1. **Start your app**:
   ```bash
   npx expo start
   ```

2. **Test on different platforms**:
   - Press `w` for web browser
   - Press `i` for iOS simulator
   - Scan QR code for physical device

3. **Verify authentication**:
   - App should show the sign-in screen
   - Click "Sign In with Okta" (displays as Okta but uses Auth0)
   - Should redirect to Auth0 login page
   - After login, should redirect back to your app

### Expected Flow

1. **User clicks "Sign In"**
2. **App redirects to Auth0** (via Expo proxy)
3. **User enters credentials** on Auth0 login page
4. **Auth0 redirects back** to `https://auth.expo.dev/@charlesrmajor/learning-coach-community`
5. **Expo proxy redirects** back to your app with auth code
6. **App exchanges code** for tokens
7. **User is authenticated** and can access protected features

## 🔍 Troubleshooting

### Common Issues

**"Invalid redirect URI"**
- Ensure all callback URLs are added to Auth0 application
- Check for typos in the Expo proxy URL
- Verify CORS origins are configured

**"Client authentication failed"**
- Ensure Token Endpoint Authentication Method is set to "None"
- Verify Client ID is correct in environment variables
- Check that PKCE is enabled (should be default for SPA)

**"Invalid scope"**
- Auth0 automatically includes `openid`, `profile`, `email` scopes
- No additional configuration needed for basic scopes

**CORS errors on web**
- Add localhost origins to Allowed Origins (CORS)
- Ensure both development ports (8081, 8082) are included

### Debug Steps

1. **Check environment variables**:
   ```bash
   npx expo start --clear
   # Look for "Auth0 Client ID is required" errors
   ```

2. **Verify Auth0 configuration**:
   - Double-check callback URLs
   - Confirm application type is SPA
   - Verify CORS origins

3. **Test network requests**:
   - Open browser dev tools
   - Check for CORS errors
   - Verify redirect URLs in network tab

## 🚀 Production Considerations

### Production Callback URLs

For production deployment, add your production URLs to Auth0:

```
https://yourapp.com/callback
https://yourapp.com
```

### Security Best Practices

1. **Use HTTPS** for all production callback URLs
2. **Limit allowed origins** to only necessary domains
3. **Configure appropriate token lifetimes** in Auth0
4. **Enable MFA** for enhanced security
5. **Monitor authentication logs** in Auth0 dashboard

### Environment-Specific Configuration

Consider using different Auth0 applications for:
- **Development**: `dev-yourorg.us.auth0.com`
- **Staging**: `staging-yourorg.us.auth0.com`
- **Production**: `yourorg.us.auth0.com`

## 📚 Additional Resources

- [Auth0 SPA Authentication Guide](https://auth0.com/docs/quickstart/spa)
- [Expo AuthSession Documentation](https://docs.expo.dev/guides/authentication/)
- [PKCE Flow Specification](https://tools.ietf.org/html/rfc7636)
- [Clean Architecture Documentation](./clean-architecture-secure-storage-auth.md)

---

**Next Steps**: After completing this setup, your app will have full authentication capabilities across mobile and web platforms using Auth0! 🎉
