# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at /Users/<USER>/dev/stride/lcc/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/dev/stride/lcc/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/dev/stride/lcc/android/app/.cxx/Debug/6z363s1j/x86/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at /Users/<USER>/dev/stride/lcc/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:42 (file)
# input_SRC at /Users/<USER>/dev/stride/lcc/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/dev/stride/lcc/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/dev/stride/lcc/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/dev/stride/lcc/android/app/.cxx/Debug/6z363s1j/x86/CMakeFiles/cmake.verify_globs")
endif()
