import baseConfig from '../../app.config.json';
import Constants from 'expo-constants';

export interface EnvConfig {
	ENV_NAME: string;
	API_URL: string; // from .env
	X_PUBLIC_KEY: string; // from .env
	DEBUG: boolean; // from .env

	CONFIG_VERSION: string; // from JSON
	FEATURE_X_ENABLED: boolean; // from JSON
}

const getEnv = (): EnvConfig => {
	const extra = process.env || {};

	return {
		ENV_NAME: extra.EXPO_PUBLIC_ENV_NAME ?? 'NO FILE!',
		API_URL: extra.EXPO_PUBLIC_API_URL ?? 'https://fallback-api.example.com',
		X_PUBLIC_KEY: extra.EXPO_PUBLIC_X_PUBLIC_KEY ?? '',
		DEBUG: extra.EXPO_PUBLIC_DEBUG ?? false,

		// config.json
		CONFIG_VERSION: baseConfig.CONFIG_VERSION ?? '0.0.0',
		FEATURE_X_ENABLED: baseConfig.FEATURE_X_ENABLED ?? false,
	};
};

export default getEnv();
