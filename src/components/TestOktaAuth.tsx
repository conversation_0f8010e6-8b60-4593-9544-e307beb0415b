/**
 * Test component for <PERSON><PERSON> authentication using the official Expo pattern
 */

import React from 'react';
import { View, Text, Button, StyleSheet } from 'react-native';
import { useOktaAuthSession } from '@/hooks/useOktaAuthSession';

export function TestOktaAuth() {
  const { signIn, authData, isLoading, error, request } = useOktaAuthSession();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Test Okta Auth (Expo Pattern)</Text>
      
      {error && (
        <Text style={styles.error}>Error: {error}</Text>
      )}
      
      {authData && (
        <View style={styles.success}>
          <Text style={styles.successText}>✅ Authentication Successful!</Text>
          <Text>Access Token: {authData.accessToken.substring(0, 20)}...</Text>
        </View>
      )}
      
      <Button
        title={isLoading ? "Authenticating..." : "Sign In with Okta"}
        onPress={signIn}
        disabled={!request || isLoading}
      />
      
      <Text style={styles.info}>
        Request ready: {request ? '✅' : '❌'}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    gap: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  error: {
    color: 'red',
    textAlign: 'center',
  },
  success: {
    backgroundColor: '#e8f5e8',
    padding: 10,
    borderRadius: 5,
  },
  successText: {
    color: 'green',
    fontWeight: 'bold',
  },
  info: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});
