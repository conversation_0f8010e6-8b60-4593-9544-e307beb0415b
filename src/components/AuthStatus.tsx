/**
 * Authentication Status Component
 * 
 * A simple component to display current authentication status and user information.
 * Useful for debugging and testing the authentication system.
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import CustomButton from '@/components/ui/CustomButton';
import { useAuthentication } from '@/hooks/useAuthentication';

interface AuthStatusProps {
  showDetails?: boolean;
}

export function AuthStatus({ showDetails = false }: AuthStatusProps) {
  const {
    isAuthenticated,
    user,
    isLoading,
    error,
    getAccessToken,
    refreshAuth,
  } = useAuthentication();

  const [accessToken, setAccessToken] = React.useState<string | null>(null);

  const handleGetToken = async () => {
    const token = await getAccessToken();
    setAccessToken(token ? `${token.substring(0, 20)}...` : null);
  };

  const handleRefresh = async () => {
    const success = await refreshAuth();
    console.log('Refresh result:', success);
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Checking authentication...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="subtitle" style={styles.title}>
        Authentication Status
      </ThemedText>
      
      <View style={styles.statusRow}>
        <ThemedText>Status: </ThemedText>
        <ThemedText style={[
          styles.statusText,
          { color: isAuthenticated ? '#4caf50' : '#f44336' }
        ]}>
          {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
        </ThemedText>
      </View>

      {user && (
        <View style={styles.userInfo}>
          <ThemedText style={styles.userText}>User: {user.name}</ThemedText>
          <ThemedText style={styles.userText}>Email: {user.email}</ThemedText>
          {user.roles.length > 0 && (
            <ThemedText style={styles.userText}>
              Roles: {user.roles.join(', ')}
            </ThemedText>
          )}
        </View>
      )}

      {error && (
        <ThemedText style={styles.errorText}>
          Error: {error}
        </ThemedText>
      )}

      {showDetails && isAuthenticated && (
        <View style={styles.detailsContainer}>
          <CustomButton
            variant="secondary"
            onPress={handleGetToken}
            style={styles.button}
          >
            Get Access Token
          </CustomButton>
          
          {accessToken && (
            <ThemedText style={styles.tokenText}>
              Token: {accessToken}
            </ThemedText>
          )}
          
          <CustomButton
            variant="secondary"
            onPress={handleRefresh}
            style={styles.button}
          >
            Refresh Auth
          </CustomButton>
        </View>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 15,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginVertical: 10,
  },
  title: {
    marginBottom: 10,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  statusText: {
    fontWeight: 'bold',
  },
  userInfo: {
    marginVertical: 10,
    padding: 10,
    backgroundColor: '#e8f5e8',
    borderRadius: 5,
  },
  userText: {
    fontSize: 14,
    marginBottom: 2,
  },
  errorText: {
    color: '#f44336',
    marginTop: 10,
  },
  detailsContainer: {
    marginTop: 15,
    gap: 10,
  },
  button: {
    alignSelf: 'flex-start',
  },
  tokenText: {
    fontSize: 12,
    fontFamily: 'monospace',
    backgroundColor: '#f0f0f0',
    padding: 5,
    borderRadius: 3,
  },
});
