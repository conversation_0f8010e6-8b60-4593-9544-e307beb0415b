/**
 * Secure Storage Domain Models
 * 
 * Core domain models for secure storage operations.
 * These models represent the business entities for data persistence.
 */

/**
 * Generic secure storage item with metadata
 */
export interface SecureStorageItem<T = any> {
  /** Storage key identifier */
  key: string;
  /** Stored value */
  value: T;
  /** Optional expiration timestamp in milliseconds */
  expiresAt?: number;
  /** Optional metadata for the stored item */
  metadata?: Record<string, any>;
  /** Timestamp when the item was created */
  createdAt: number;
  /** Timestamp when the item was last updated */
  updatedAt: number;
}

/**
 * Result of a storage operation
 */
export interface StorageResult<T = any> {
  /** Whether the operation was successful */
  success: boolean;
  /** Result data (if successful) */
  data?: T;
  /** Error message (if failed) */
  error?: string;
  /** Optional operation metadata */
  metadata?: {
    /** Time taken for the operation in milliseconds */
    duration?: number;
    /** Storage key involved in the operation */
    key?: string;
    /** Operation type */
    operation?: StorageOperation;
  };
}

/**
 * Types of storage operations
 */
export enum StorageOperation {
  SET = 'SET',
  GET = 'GET',
  REMOVE = 'REMOVE',
  CLEAR = 'CLEAR',
  EXISTS = 'EXISTS',
}

/**
 * Storage configuration options
 */
export interface StorageConfig {
  /** Default expiration time in milliseconds */
  defaultExpirationMs?: number;
  /** Whether to encrypt stored data */
  encrypt?: boolean;
  /** Maximum storage size in bytes */
  maxSizeBytes?: number;
  /** Storage key prefix */
  keyPrefix?: string;
}

/**
 * Storage statistics and information
 */
export interface StorageInfo {
  /** Total number of stored items */
  itemCount: number;
  /** Total storage size in bytes */
  totalSizeBytes: number;
  /** Available storage space in bytes */
  availableSizeBytes?: number;
  /** Storage provider name */
  provider: string;
  /** Whether the storage is available */
  isAvailable: boolean;
}

/**
 * Storage key patterns for different data types
 */
export const StorageKeys = {
  /** Authentication data */
  AUTH_DATA: 'auth_data',
  /** User profile information */
  USER_PROFILE: 'user_profile',
  /** User preferences */
  USER_PREFERENCES: 'user_preferences',
  /** API configuration */
  API_CONFIG: 'api_config',
  /** Session data */
  SESSION_DATA: 'session_data',
  /** Temporary cache data */
  CACHE_PREFIX: 'cache_',
  /** Application settings */
  APP_SETTINGS: 'app_settings',
} as const;

export type StorageKey = (typeof StorageKeys)[keyof typeof StorageKeys] | string;

/**
 * Storage events that can be emitted
 */
export enum StorageEvent {
  ITEM_SET = 'ITEM_SET',
  ITEM_GET = 'ITEM_GET',
  ITEM_REMOVED = 'ITEM_REMOVED',
  STORAGE_CLEARED = 'STORAGE_CLEARED',
  STORAGE_ERROR = 'STORAGE_ERROR',
  ITEM_EXPIRED = 'ITEM_EXPIRED',
}

/**
 * Storage event data
 */
export interface StorageEventData {
  event: StorageEvent;
  key?: string;
  error?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

/**
 * Storage migration information
 */
export interface StorageMigration {
  /** Migration version */
  version: number;
  /** Migration description */
  description: string;
  /** Migration function */
  migrate: (currentData: Record<string, any>) => Promise<Record<string, any>>;
}

/**
 * Batch storage operation
 */
export interface BatchStorageOperation<T = any> {
  /** Operation type */
  operation: StorageOperation;
  /** Storage key */
  key: string;
  /** Value (for SET operations) */
  value?: T;
}

/**
 * Batch storage result
 */
export interface BatchStorageResult {
  /** Whether all operations were successful */
  success: boolean;
  /** Individual operation results */
  results: StorageResult[];
  /** Overall error message (if any operations failed) */
  error?: string;
}
