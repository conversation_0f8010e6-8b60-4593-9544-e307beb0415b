// Jest setup file to configure global mocks and polyfills

// Mock global 'self' for Okta Auth JS which expects a browser environment
global.self = global;

// Mock window object for React Native components that might reference it
global.window = global;

// Mock console methods to reduce noise in tests
global.console = {
	...console,
	// Uncomment to silence specific console methods during tests
	// log: jest.fn(),
	// debug: jest.fn(),
	// info: jest.fn(),
	// warn: jest.fn(),
	// error: jest.fn(),
};

// Mock React Native Alert
jest.mock('react-native', () => {
	const RN = jest.requireActual('react-native');
	return {
		...RN,
		Alert: {
			alert: jest.fn(),
		},
	};
});

// Mock Expo Router
jest.mock('expo-router', () => ({
	useNavigation: () => ({
		setOptions: jest.fn(),
		navigate: jest.fn(),
		goBack: jest.fn(),
	}),
	useRouter: () => ({
		push: jest.fn(),
		replace: jest.fn(),
		back: jest.fn(),
	}),
	useLocalSearchParams: () => ({}),
	useGlobalSearchParams: () => ({}),
}));

// Mock Expo SecureStore
jest.mock('expo-secure-store', () => ({
	setItemAsync: jest.fn(),
	getItemAsync: jest.fn(),
	deleteItemAsync: jest.fn(),
	isAvailableAsync: jest.fn(() => Promise.resolve(true)),
}));

// Mock React Native Async Storage
jest.mock('@react-native-async-storage/async-storage', () => ({
	setItem: jest.fn(),
	getItem: jest.fn(),
	removeItem: jest.fn(),
	clear: jest.fn(),
	getAllKeys: jest.fn(),
	multiGet: jest.fn(),
	multiSet: jest.fn(),
	multiRemove: jest.fn(),
}));
