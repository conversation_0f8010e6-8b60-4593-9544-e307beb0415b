# Okta SSO Integration Plan – Option A (Middleware S2S to UNA)

Status: Draft v1
Owner: Architecture
Last Updated: 2025-08-20

## Goals
- Preserve existing Okta SSO for UNA web app with no disruption.
- Add OIDC Code + PKCE sign-in for:
  - Expo React Native (iOS/Android) – mobile priority.
  - Web (React Native Web) – same login, same users.
- Middleware validates Okta JWTs and authorizes requests; Middleware uses S2S integration to UNA (no direct client auth to UNA).

## Scope & Non-Goals
- Keep current UNA Okta app integration as-is (mid-term).
- Do not change UNA user provisioning or roles in UNA at this time.
- New Okta apps and a (recommended) custom Authorization Server for the Middleware/API audience.

---

## Step 0 – Inventory Current Okta/UNA Setup
- __[confirm]__ Okta Org/Domain (e.g., `yourOrg.okta.com` or `okta.yourdomain.com`).
- __[confirm]__ Current UNA Okta App Integration type (OIDC/SAML), Client ID, Redirect URIs.
- __[confirm]__ Current Authorization Server used by UNA (default vs custom).
- __[confirm]__ Groups/claims used today for UNA roles.
- __[confirm]__ Environments (dev/stage/prod) and whether separate Okta tenants/apps exist per environment.

## Step 1 – Environment Plan
- __Recommendation__: Separate Okta applications per environment with distinct redirect URIs and client IDs.
- Define environment domains:
  - Web: `https://app.{env}.example.com` (prod omits env)
  - API: `https://api.{env}.example.com`
  - Native scheme: `com.company.app{env}:/callback` (e.g., `com.company.app:/callback` for prod)

## Step 2 – Create/Configure Authorization Server
- Use a __Custom Authorization Server__ (recommended) to isolate audiences/scopes for the Middleware/API.
  - Issuer: `https://{okta_domain}/oauth2/{auth_server_id}`
  - Audience: `api://stride-middleware`
- __Scopes__ (example – adjust per need):
  - `openid`, `profile`, `email`, `offline_access`
  - `stride.read`, `stride.write`
  - `una.read`, `una.write`
- __Claims__ (customize mappings):
  - `groups`: include Okta groups via regex filter (e.g., `^stride-.*`)
  - `role`: map from groups to normalized roles (e.g., `stride-admin`, `stride-teacher`, `stride-student`)
  - `tenant`: from Okta profile attribute or group/claim mapping
- __Token lifetimes__:
  - Access Token: 5–10 min
  - ID Token: 5–10 min
  - Refresh Token: 30 days (rotation enabled)
- __CORS & Trusted Origins__: Add Web origins to Okta Trusted Origins for CORS/Redirect.

## Step 3 – Create App: Native (Expo iOS/Android)
- Type: OIDC __Native App__ (Public client), __Code + PKCE__, refresh tokens enabled with rotation.
- Redirect URIs:
  - Prod: `com.company.app:/callback`
  - Stage: `com.company.app-stage:/callback`
  - Dev: `com.company.app-dev:/callback`
  - Expo Dev (if using expo-auth-session): `https://auth.expo.dev/@<org>/<slug>`
- Post Logout Redirect URIs: same scheme with `/logout` (optional) or use in-app handling.
- Allowed Scopes: `openid profile email offline_access stride.read stride.write una.read una.write`.
- Assign users/groups appropriate to mobile access.

## Step 4 – Create App: Web (SPA – React Native Web)
- Type: OIDC __SPA__ (Public client), __Code + PKCE__, refresh tokens with rotation.
- Redirect URIs:
  - Dev: `http://localhost:5173/callback` (or your dev port)
  - Stage: `https://app.stage.example.com/callback`
  - Prod: `https://app.example.com/callback`
- Post Logout Redirect URIs:
  - Dev: `http://localhost:5173/logout`
  - Stage/Prod: `https://app.{env}.example.com/logout`
- Trusted Origins (CORS + Redirect): add all Web origins.
- Allowed Scopes: same as Native.

## Client SDK Choice (Expo vs Okta SDKs)
- Expo Managed (iOS/Android/Web, single code path): use `expo-auth-session` with Code + PKCE and refresh rotation.
- Prebuild/Bare (mobile) and separate Web app: consider Okta SDKs for mobile; web can use `@okta/okta-auth-js`/`@okta/okta-react`.
- Tradeoffs:
  - AuthSession: no native modules; consistent across platforms; you implement storage/refresh/logout.
  - Okta SDKs: IdP helpers and native UX; requires native builds; separate web/mobile paths.

## Web SSO Reuse Recommendations
- Use full-page redirect to Okta /authorize; Okta SSO session cookie will auto-login if present.
- Avoid silent iframe `prompt=none` due to third-party cookie restrictions.
- Use refresh token rotation; keep access tokens in memory; prefer no localStorage for access tokens.
- On page reload with no tokens, redirect again; login UI will be skipped if Okta session is valid.

## Step 5 – Groups, Roles, and Claims Mapping
- Create or reuse Okta groups (e.g., `stride-admin`, `stride-teacher`, `stride-student`).
- Authorization Server claim rules:
  - `groups` claim filtered by regex (only `stride-*`).
  - `role` claim: map groups -> canonical app role (string or array).
  - `tenant` claim: derive from Okta profile attribute or group naming convention (e.g., `tenant:district-123`).
- Document mapping in `docs/rbac-mapping.md` (to be created).

## Step 6 – Middleware/API Integration
- Clients (Mobile/Web) obtain tokens from Okta via Code + PKCE.
- Clients call Middleware with `Authorization: Bearer <access_token>`.
- Middleware validates JWTs and authorizes per claims.
- __Issuer__: `https://{okta_domain}/oauth2/{auth_server_id}`
- __Audience__: `api://stride-middleware`
- __JWKS URI__: `https://{okta_domain}/oauth2/{auth_server_id}/v1/keys`

### Language/Hosting Dependencies (highlighted)
- __JWT Validation Library__
  - Node/NestJS: `jose` or `passport-jwt`/`@nestjs/passport`; middleware/guard to enforce issuer/audience.
  - Python/FastAPI: `python-jose`/`authlib`; dependency to cache JWKS and verify.
  - Java/Spring Boot: Spring Security Resource Server (`spring-boot-starter-oauth2-resource-server`).
  - .NET: `Microsoft.AspNetCore.Authentication.JwtBearer` with Authority/Audience.
- __API Gateway Integration__ (if applicable)
  - AWS API Gateway/Lambda Authorizer or JWT Authorizer configured with Issuer/Audience.
  - Kubernetes Ingress + external auth (e.g., OPA/Envoy ext_authz) to offload JWT checks.
- __Internal JWT (optional)__
  - If minting an internal short-lived app token, choose signing algorithm/key store (e.g., AWS KMS) and library per stack.
  - Define token lifetime (e.g., 2–5 min), audience (internal services), and rotation policy.

## Step 7 – Preserve UNA SSO
- __No change__ to the existing UNA Okta app integration for mid-term.
- Ensure users belong to the same Okta directory/tenant as Mobile/Web.
- If needed, align group assignments so roles remain consistent across UNA and the new App.
- Middleware uses UNA S2S auth (not Okta) for UNA API access per `docs/architecture.md`.

## Step 8 – Expo App Configuration
- Use `expo-auth-session` with __Code + PKCE__.
- Configure `scheme` in `app.json/app.config.ts` per environment.
- For dev, add Expo redirect: `https://auth.expo.dev/@<org>/<slug>`.
- iOS: add associated domains if using universal links (optional) and URL Types for custom scheme.
- Android: intent filter for custom scheme.

## MVP: RN Login Without Middleware
- Feasible now using `expo-auth-session` with OIDC Code + PKCE.
- Minimum required (no backend):
  - Okta Native app configured (clientId, redirect URIs, scopes).
  - Implement auth flow; decode `id_token` for profile; store refresh token in `expo-secure-store`; keep access token in memory.
  - Optional: Call Okta `userinfo` endpoint with the access token to display profile.
- To demo backend validation later (minimal):
  - Spin up a tiny Middleware stub that validates JWTs via JWKS (issuer/audience) and exposes `/me` returning token claims.
  - No DB or UNA integration required for this demo.

## Step 9 – Web App Configuration (RN Web)
- Use Okta OIDC PKCE client (Okta Auth JS) or a standards-compliant OIDC PKCE library.
- Store tokens in memory; avoid localStorage for access tokens (XSS risk). Use refresh tokens with rotation.
- Configure issuer, clientId, redirectUri, scopes, and audience (via `resource` or backend enforcement).

## Step 10 – Okta Trusted Origins, CORS, and Redirects
- Add all Web origins to Trusted Origins for CORS and Redirect.
- For SPA, enable CORS for the Authorization Server endpoints if required by your library.

## Step 11 – Security Defaults
- Enforce PKCE and refresh token rotation.
- Validate `iss`, `aud`, `exp`, `nbf`, and signature in Middleware.
- Enforce per-route RBAC/ABAC using `groups`, `role`, `tenant` claims.
- Set rate limits at API gateway or Middleware.

## Step 12 – Testing Plan
- Postman: Configure OAuth2 with Okta issuer/client for SPA to obtain tokens; call Middleware endpoints.
- Mobile Dev: Use Expo to sign in via Okta; verify API calls succeed with token; test token refresh.
- Web Dev: Sign in on localhost; verify redirects and token flows; test logout.
- Negative tests: expired token, wrong audience, revoked refresh, missing claim.

## Step 13 – Rollout Plan
- Create dev environment first; validate end-to-end.
- Stage with limited pilot users/groups.
- Production with canary cohort; monitor auth error rates and latency.
- Keep UNA SSO unchanged; clearly communicate dual entry points during transition.

## Artifacts to Produce
- Okta App IDs, Client IDs per env (Native and SPA).
- Authorization Server ID, issuer URL, JWKS URL, audience string.
- Claim mapping rules and regex for `groups`.
- List of permitted redirect URIs per env.

## Open Questions
- __Okta tenancy__: Single vs per-environment tenants? If single, are environment apps sufficiently isolated by groups/policies?
- __RBAC mapping__: Which Okta groups drive roles? Do we need a `roles` claim separate from `groups`?
- __Tenant claim__: Source of truth for tenant; is it an Okta profile attribute or group naming convention?
- __Refresh policy__: Desired refresh token lifetime and rotation policy for mobile vs web.
- __API Gateway__: Will we offload JWT validation to gateway (e.g., AWS API Gateway) or do it in-app?
- __Libraries__: Preference for Middleware language/framework to finalize validation libraries and examples.

## References
- `docs/architecture.md` – Overall architecture and flows (Option A).
- Okta Docs – OIDC, PKCE, SPA & Native apps, Custom Authorization Servers, Groups claims.
