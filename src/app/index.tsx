/**
 * Default Index Route
 * 
 * Redirects to okta-demo for Android testing
 */

import { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { View, Text, StyleSheet } from 'react-native';

export default function IndexScreen() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to okta-demo immediately
    router.replace('/okta-demo');
  }, [router]);

  return (
    <View style={styles.container}>
      <Text style={styles.text}>Redirecting to Okta Demo...</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  text: {
    fontSize: 16,
    color: '#6B7280',
  },
});
