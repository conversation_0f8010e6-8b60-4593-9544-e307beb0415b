/**
 * Okta Expo Authentication Provider
 *
 * Implementation following Expo's official documentation pattern for Okta integration.
 * Uses expo-auth-session hooks for a simpler, more reliable implementation.
 */

import * as WebBrowser from 'expo-web-browser';
import * as AuthSession from 'expo-auth-session';
import { Platform } from 'react-native';
import { IOktaAuthProvider } from './interfaces';
import { AuthenticationData, AuthenticatedUser, AuthConfig, AuthResult } from '@/types/auth';

// Complete auth session for web
WebBrowser.maybeCompleteAuthSession();

/**
 * Okta Expo implementation using the official Expo pattern
 */
export class OktaExpoAuthProvider implements IOktaAuthProvider {
  private issuer: string;
  private clientId: string;
  private redirectUri: string;
  private scopes: string[];
  private currentTokens: AuthenticationData | null = null;
  private discoveryDocument: any = null;

  constructor(config?: Partial<AuthConfig>) {
    // Use environment variables with config overrides
    this.issuer = config?.issuer || process.env.EXPO_PUBLIC_OKTA_ISSUER!;
    this.clientId = config?.clientId || process.env.EXPO_PUBLIC_OKTA_CLIENT_ID!;
    this.scopes = config?.scopes || ['openid', 'profile'] || ['openid', 'profile', 'email', 'offline_access'];

    // Determine redirect URI based on environment and config
    if (config?.redirectUri) {
      this.redirectUri = config.redirectUri;
    } else {
      // Use Okta's required redirect URI pattern as per Expo docs
      // Okta requires: com.okta.<OKTA_DOMAIN>:/callback
      // Based on the issuer domain (integrator-5743111.okta.com), the correct pattern is:
      // Note: Using direct string instead of makeRedirectUri to avoid Expo dev server override
      this.redirectUri = 'com.okta.integrator-5743111:/callback';

      // Alternative: Use app scheme (if Okta allows it)
      // this.redirectUri = AuthSession.makeRedirectUri({
      //   scheme: 'learningcoachcommunity',
      //   path: 'callback',
      // });
    }

    if (!this.issuer) {
      throw new Error('Okta Issuer is required for Expo authentication');
    }

    if (!this.clientId) {
      throw new Error('Okta Client ID is required for Expo authentication');
    }

    // Initialize discovery document
    this.initializeDiscovery();
  }

  private async initializeDiscovery(): Promise<void> {
    try {
      this.discoveryDocument = await AuthSession.fetchDiscoveryAsync(this.issuer);
    } catch (error) {
      console.error('Failed to fetch discovery document:', error);
    }
  }

  /**
   * Initiate sign-in process using AuthSession.promptAsync
   */
  async signIn(): Promise<AuthenticationData> {
    try {
      // Check if already authenticated
      if (this.currentTokens && !this.isTokenExpiredSync(this.currentTokens)) {
        return this.currentTokens;
      }

      // Ensure discovery document is loaded
      if (!this.discoveryDocument) {
        await this.initializeDiscovery();
      }

      if (!this.discoveryDocument) {
        throw new Error('Failed to load Okta discovery document');
      }

      // Debug: Log the redirect URI being used
      console.log('🔐 [OktaExpoAuthProvider] Using redirect URI:', this.redirectUri);
      console.log('🔐 [OktaExpoAuthProvider] Client ID:', this.clientId);
      console.log('🔐 [OktaExpoAuthProvider] Issuer:', this.issuer);
      console.log('🔐 [OktaExpoAuthProvider] Scopes:', this.scopes);
      console.log('🔐 [OktaExpoAuthProvider] Discovery document:', this.discoveryDocument);

      // Create auth request
      const authRequest = new AuthSession.AuthRequest({
        clientId: this.clientId,
        scopes: this.scopes,
        redirectUri: this.redirectUri,
        responseType: AuthSession.ResponseType.Code,
      });

      console.log('🔐 [OktaExpoAuthProvider] Auth request created:', {
        clientId: authRequest.clientId,
        redirectUri: authRequest.redirectUri,
        responseType: authRequest.responseType,
        scopes: authRequest.scopes,
      });

      // Prompt for authentication
      const result = await authRequest.promptAsync(this.discoveryDocument);

      if (result.type === 'success') {
        return await this.handleAuthResponse(result, this.discoveryDocument);
      } else if (result.type === 'cancel') {
        throw new Error('Authentication was cancelled by user');
      } else {
        throw new Error(`Authentication failed: ${result.type}`);
      }
    } catch (error) {
      console.error('Sign in failed:', error);
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create auth request configuration for use with hooks
   * This method can be used in React components that want to use the useAuthRequest hook
   */
  createAuthRequestConfig() {
    return {
      clientId: this.clientId,
      scopes: this.scopes,
      redirectUri: this.redirectUri,
      responseType: AuthSession.ResponseType.Code,
    };
  }

  /**
   * Handle the authentication response from the auth request
   */
  async handleAuthResponse(response: any, discovery: any): Promise<AuthenticationData> {
    try {
      if (response?.type === 'success') {
        const { code } = response.params;
        
        if (!code) {
          throw new Error('No authorization code received');
        }

        // Exchange code for tokens
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: this.clientId,
            code,
            redirectUri: this.redirectUri,
          },
          discovery
        );

        const authData: AuthenticationData = {
          accessToken: tokenResult.accessToken,
          refreshToken: tokenResult.refreshToken,
          idToken: tokenResult.idToken,
          expiresAt: Date.now() + (tokenResult.expiresIn ? tokenResult.expiresIn * 1000 : 3600 * 1000),
          tokenType: 'Bearer',
          scope: this.scopes,
        };

        this.currentTokens = authData;
        return authData;
      } else if (response?.type === 'cancel') {
        throw new Error('Authentication was cancelled by user');
      } else {
        throw new Error(`Authentication failed: ${response?.type || 'unknown'}`);
      }
    } catch (error) {
      console.error('Auth response handling failed:', error);
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    try {
      // Clear stored tokens
      this.currentTokens = null;
      console.log('User signed out successfully');
    } catch (error) {
      console.error('Sign out failed:', error);
      throw new Error(`Sign out failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Refresh the current authentication token
   */
  async refreshToken(): Promise<AuthenticationData> {
    try {
      if (!this.currentTokens?.refreshToken) {
        throw new Error('No refresh token available');
      }

      // For now, return current tokens
      // In a full implementation, you'd call the refresh endpoint
      return this.currentTokens;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw new Error(`Token refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      return this.currentTokens !== null && !this.isTokenExpiredSync(this.currentTokens);
    } catch (error) {
      console.error('Authentication check failed:', error);
      return false;
    }
  }

  private isTokenExpiredSync(tokens: AuthenticationData): boolean {
    return Date.now() >= tokens.expiresAt;
  }

  /**
   * Get current authenticated user information
   */
  async getCurrentUser(): Promise<AuthenticatedUser | null> {
    try {
      if (!this.currentTokens?.idToken) {
        return null;
      }

      // Decode ID token to get user info
      const user = this.decodeIdToken(this.currentTokens.idToken);
      if (!user) {
        return null;
      }

      return {
        id: user.sub!,
        email: user.email!,
        name: user.name || `${user.given_name || ''} ${user.family_name || ''}`.trim(),
        roles: Array.isArray(user.groups) ? user.groups as string[] : typeof user.groups === 'string' ? [user.groups] : [],
        tenant: typeof user.tenant === 'string' ? user.tenant : undefined,
        groups: Array.isArray(user.groups) ? user.groups as string[] : typeof user.groups === 'string' ? [user.groups] : [],
        attributes: {
          given_name: user.given_name,
          family_name: user.family_name,
          preferred_username: user.preferred_username,
          locale: user.locale,
          zoneinfo: user.zoneinfo,
        },
      };
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  private decodeIdToken(idToken: string): any {
    try {
      const payload = idToken.split('.')[1];
      const decoded = JSON.parse(atob(payload));
      return decoded;
    } catch (error) {
      console.error('Failed to decode ID token:', error);
      return null;
    }
  }

  /**
   * Get current access token
   */
  async getAccessToken(): Promise<string | null> {
    try {
      return this.currentTokens?.accessToken || null;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  /**
   * Get current ID token
   */
  async getIdToken(): Promise<string | null> {
    try {
      return this.currentTokens?.idToken || null;
    } catch (error) {
      console.error('Failed to get ID token:', error);
      return null;
    }
  }

  /**
   * Check if current token is expired
   */
  async isTokenExpired(): Promise<boolean> {
    try {
      return this.currentTokens ? this.isTokenExpiredSync(this.currentTokens) : true;
    } catch (error) {
      console.error('Failed to check token expiration:', error);
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  async getTokenExpiration(): Promise<number | null> {
    try {
      return this.currentTokens?.expiresAt || null;
    } catch (error) {
      console.error('Failed to get token expiration:', error);
      return null;
    }
  }

  /**
   * Get the underlying Okta Auth client instance (not applicable for Expo)
   */
  getOktaAuthClient(): any {
    return {
      issuer: this.issuer,
      clientId: this.clientId,
      redirectUri: this.redirectUri,
    };
  }

  /**
   * Handle authentication callback (not needed for Expo AuthSession)
   */
  async handleCallback(): Promise<AuthenticationData> {
    if (!this.currentTokens) {
      throw new Error('No authentication data available');
    }
    return this.currentTokens;
  }

  /**
   * Sign in with redirect (not applicable for Expo)
   */
  async signInWithRedirect(originalUri?: string): Promise<void> {
    throw new Error('signInWithRedirect not supported in Expo. Use createAuthRequest() instead.');
  }

  /**
   * Handle silent authentication
   */
  async silentAuthenticate(): Promise<AuthResult<AuthenticationData>> {
    try {
      const authenticated = await this.isAuthenticated();
      if (authenticated && this.currentTokens) {
        return {
          success: true,
          data: this.currentTokens,
        };
      }

      return {
        success: false,
        error: 'User not authenticated',
      };
    } catch (error) {
      console.error('Silent authentication failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Silent authentication failed',
      };
    }
  }

  /**
   * Get user information
   */
  async getUserInfo(): Promise<any> {
    try {
      return await this.getCurrentUser();
    } catch (error) {
      console.error('Failed to get user info:', error);
      throw new Error(`Failed to get user info: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Revoke tokens
   */
  async revokeTokens(accessToken?: string, refreshToken?: string): Promise<void> {
    try {
      this.currentTokens = null;
      console.log('Tokens revoked locally');
    } catch (error) {
      console.error('Token revocation failed:', error);
      throw new Error(`Token revocation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
