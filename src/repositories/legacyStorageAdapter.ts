/**
 * Legacy Storage Adapter
 * 
 * Compatibility layer that implements the old IStorageRepository interface
 * using the new Clean Architecture secure storage system.
 * 
 * This allows existing code to continue working while we migrate to the new system.
 */

import { ServiceFactory } from '@/services/serviceFactory';
import { SecureStorageService } from '@/services/secureStorageService';
import { IStorageRepository } from '@/types';

/**
 * Adapter that implements the legacy IStorageRepository interface
 * using the new SecureStorageService
 */
class LegacyStorageAdapter implements IStorageRepository {
  private storageService: SecureStorageService;

  constructor() {
    this.storageService = ServiceFactory.createSecureStorageService();
  }

  /**
   * Get item using legacy interface
   */
  async getItem(key: string): Promise<string | null> {
    try {
      const result = await this.storageService.getSecureData<string>(key);
      return result.success ? result.data || null : null;
    } catch (error) {
      console.error(`Legacy adapter: Failed to get item ${key}:`, error);
      return null;
    }
  }

  /**
   * Set item using legacy interface
   */
  async setItem(key: string, value: string): Promise<void> {
    try {
      const result = await this.storageService.storeSecureData(key, value);
      if (!result.success) {
        throw new Error(result.error || 'Storage operation failed');
      }
    } catch (error) {
      console.error(`Legacy adapter: Failed to set item ${key}:`, error);
      throw error;
    }
  }

  /**
   * Remove item using legacy interface
   */
  async removeItem(key: string): Promise<void> {
    try {
      const result = await this.storageService.removeSecureData(key);
      if (!result.success) {
        throw new Error(result.error || 'Remove operation failed');
      }
    } catch (error) {
      console.error(`Legacy adapter: Failed to remove item ${key}:`, error);
      throw error;
    }
  }

  /**
   * Check if item exists (legacy method)
   */
  async hasItem(key: string): Promise<boolean> {
    try {
      const result = await this.storageService.hasData(key);
      return result.success ? result.data || false : false;
    } catch (error) {
      console.error(`Legacy adapter: Failed to check item ${key}:`, error);
      return false;
    }
  }
}

/**
 * Singleton instance for backward compatibility
 */
export const legacyStorageRepository = new LegacyStorageAdapter();

/**
 * Export the old StorageKeys for compatibility
 * @deprecated Use StorageKeys from @/types/storage instead
 */
export const StorageKeys = {
  AUTH_DATA: 'auth_data',
  USER_PROFILE: 'user_profile',
  USER_PREFERENCES: 'user_preferences',
  API_CONFIG: 'api_config',
  APP_SETTINGS: 'app_settings',
} as const;
