/**
 * Repository Layer Interfaces
 * 
 * Abstract interfaces for data access operations.
 * These interfaces define contracts that platform-specific implementations must follow.
 */

import { AuthenticationData, AuthenticatedUser } from '@/types/auth';
import { StorageResult, StorageInfo, BatchStorageOperation, BatchStorageResult } from '@/types/storage';

/**
 * Core secure storage repository interface
 * Provides basic key-value storage operations with security
 */
export interface ISecureStorageRepository {
  /**
   * Store a string value securely
   * @param key Storage key
   * @param value String value to store
   */
  setItem(key: string, value: string): Promise<void>;

  /**
   * Retrieve a string value securely
   * @param key Storage key
   * @returns Stored value or null if not found
   */
  getItem(key: string): Promise<string | null>;

  /**
   * Remove a stored item
   * @param key Storage key
   */
  removeItem(key: string): Promise<void>;

  /**
   * Clear all stored items
   */
  clear(): Promise<void>;

  /**
   * Check if a key exists in storage
   * @param key Storage key
   * @returns True if key exists
   */
  exists(key: string): Promise<boolean>;

  /**
   * Store a typed object securely
   * @param key Storage key
   * @param value Object to store
   */
  setObject<T>(key: string, value: T): Promise<void>;

  /**
   * Retrieve a typed object securely
   * @param key Storage key
   * @returns Stored object or null if not found
   */
  getObject<T>(key: string): Promise<T | null>;

  /**
   * Remove a stored object
   * @param key Storage key
   */
  removeObject(key: string): Promise<void>;

  /**
   * Get storage information and statistics
   * @returns Storage information
   */
  getStorageInfo(): Promise<StorageInfo>;
}

/**
 * Authentication-specific storage repository interface
 * Extends basic storage with authentication-specific operations
 */
export interface IAuthStorageRepository extends ISecureStorageRepository {
  /**
   * Store authentication data securely
   * @param authData Authentication data to store
   */
  saveAuthData(authData: AuthenticationData): Promise<void>;

  /**
   * Retrieve stored authentication data
   * @returns Authentication data or null if not found
   */
  getAuthData(): Promise<AuthenticationData | null>;

  /**
   * Clear all authentication data
   */
  clearAuthData(): Promise<void>;

  /**
   * Store user profile information
   * @param user User profile to store
   */
  saveUserProfile(user: AuthenticatedUser): Promise<void>;

  /**
   * Retrieve stored user profile
   * @returns User profile or null if not found
   */
  getUserProfile(): Promise<AuthenticatedUser | null>;

  /**
   * Check if authentication data exists and is valid
   * @returns True if valid auth data exists
   */
  hasValidAuthData(): Promise<boolean>;
}

/**
 * Session storage repository interface (web-specific)
 * For temporary data that should be cleared when session ends
 */
export interface ISessionStorageRepository {
  /**
   * Store a session item
   * @param key Storage key
   * @param value String value to store
   */
  setSessionItem(key: string, value: string): Promise<void>;

  /**
   * Retrieve a session item
   * @param key Storage key
   * @returns Stored value or null if not found
   */
  getSessionItem(key: string): Promise<string | null>;

  /**
   * Remove a session item
   * @param key Storage key
   */
  removeSessionItem(key: string): Promise<void>;

  /**
   * Clear all session data
   */
  clearSession(): Promise<void>;

  /**
   * Store a typed object in session
   * @param key Storage key
   * @param value Object to store
   */
  setSessionObject<T>(key: string, value: T): Promise<void>;

  /**
   * Retrieve a typed object from session
   * @param key Storage key
   * @returns Stored object or null if not found
   */
  getSessionObject<T>(key: string): Promise<T | null>;
}

/**
 * Preferences storage repository interface
 * For non-sensitive, persistent user preferences
 */
export interface IPreferencesRepository {
  /**
   * Store a preference value
   * @param key Preference key
   * @param value Preference value
   */
  setPreference(key: string, value: string): Promise<void>;

  /**
   * Retrieve a preference value
   * @param key Preference key
   * @returns Preference value or null if not found
   */
  getPreference(key: string): Promise<string | null>;

  /**
   * Remove a preference
   * @param key Preference key
   */
  removePreference(key: string): Promise<void>;

  /**
   * Clear all preferences
   */
  clearPreferences(): Promise<void>;

  /**
   * Store a typed preference object
   * @param key Preference key
   * @param value Preference object
   */
  setPreferenceObject<T>(key: string, value: T): Promise<void>;

  /**
   * Retrieve a typed preference object
   * @param key Preference key
   * @returns Preference object or null if not found
   */
  getPreferenceObject<T>(key: string): Promise<T | null>;
}

/**
 * Enhanced storage repository with advanced features
 */
export interface IEnhancedStorageRepository extends ISecureStorageRepository {
  /**
   * Perform batch storage operations
   * @param operations Array of storage operations
   * @returns Batch operation results
   */
  batch(operations: BatchStorageOperation[]): Promise<BatchStorageResult>;

  /**
   * Store item with expiration
   * @param key Storage key
   * @param value Value to store
   * @param expirationMs Expiration time in milliseconds
   */
  setItemWithExpiration<T>(key: string, value: T, expirationMs: number): Promise<void>;

  /**
   * Get all keys in storage
   * @returns Array of all storage keys
   */
  getAllKeys(): Promise<string[]>;

  /**
   * Get multiple items at once
   * @param keys Array of storage keys
   * @returns Map of key-value pairs
   */
  getMultiple(keys: string[]): Promise<Map<string, string | null>>;

  /**
   * Remove multiple items at once
   * @param keys Array of storage keys
   */
  removeMultiple(keys: string[]): Promise<void>;
}
