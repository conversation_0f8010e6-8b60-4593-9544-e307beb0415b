/**
 * Mobile Secure Storage Repository
 * 
 * Implementation of secure storage for mobile platforms using expo-secure-store.
 * Provides encrypted storage using iOS Keychain and Android Keystore.
 */

import * as SecureStore from 'expo-secure-store';
import { IAuthStorageRepository } from '../interfaces';
import { AuthenticationData, AuthenticatedUser } from '@/types/auth';
import { StorageInfo, StorageKeys } from '@/types/storage';

/**
 * Mobile implementation of secure storage using expo-secure-store
 */
export class MobileSecureStorageRepository implements IAuthStorageRepository {
  private readonly keyPrefix: string = 'lcc_';

  constructor() {
    // Verify SecureStore is available
    if (!SecureStore.isAvailableAsync) {
      console.warn('SecureStore is not available on this platform');
    }
  }

  /**
   * Store a string value securely using expo-secure-store
   */
  async setItem(key: string, value: string): Promise<void> {
    try {
      const prefixedKey = this.getPrefixedKey(key);
      await SecureStore.setItemAsync(prefixedKey, value);
    } catch (error) {
      console.error(`Failed to store item with key ${key}:`, error);
      throw new Error(`Storage operation failed: ${error}`);
    }
  }

  /**
   * Retrieve a string value securely
   */
  async getItem(key: string): Promise<string | null> {
    try {
      const prefixedKey = this.getPrefixedKey(key);
      return await SecureStore.getItemAsync(prefixedKey);
    } catch (error) {
      console.error(`Failed to retrieve item with key ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove a stored item
   */
  async removeItem(key: string): Promise<void> {
    try {
      const prefixedKey = this.getPrefixedKey(key);
      await SecureStore.deleteItemAsync(prefixedKey);
    } catch (error) {
      console.error(`Failed to remove item with key ${key}:`, error);
      throw new Error(`Removal operation failed: ${error}`);
    }
  }

  /**
   * Clear all stored items (removes known keys)
   */
  async clear(): Promise<void> {
    try {
      const keys = await this.getAllKeys();
      await Promise.all(keys.map(key => this.removeItem(key)));
    } catch (error) {
      console.error('Failed to clear storage:', error);
      throw new Error(`Clear operation failed: ${error}`);
    }
  }

  /**
   * Check if a key exists in storage
   */
  async exists(key: string): Promise<boolean> {
    try {
      const value = await this.getItem(key);
      return value !== null;
    } catch (error) {
      console.error(`Failed to check existence of key ${key}:`, error);
      return false;
    }
  }

  /**
   * Store a typed object securely
   */
  async setObject<T>(key: string, value: T): Promise<void> {
    try {
      const jsonString = JSON.stringify(value);
      await this.setItem(key, jsonString);
    } catch (error) {
      console.error(`Failed to store object with key ${key}:`, error);
      throw new Error(`Object storage operation failed: ${error}`);
    }
  }

  /**
   * Retrieve a typed object securely
   */
  async getObject<T>(key: string): Promise<T | null> {
    try {
      const jsonString = await this.getItem(key);
      if (jsonString === null) {
        return null;
      }
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error(`Failed to retrieve object with key ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove a stored object
   */
  async removeObject(key: string): Promise<void> {
    await this.removeItem(key);
  }

  /**
   * Get storage information and statistics
   */
  async getStorageInfo(): Promise<StorageInfo> {
    try {
      const keys = await this.getAllKeys();
      let totalSize = 0;

      // Calculate approximate size by getting all values
      for (const key of keys) {
        const value = await this.getItem(key);
        if (value) {
          totalSize += new Blob([value]).size;
        }
      }

      return {
        itemCount: keys.length,
        totalSizeBytes: totalSize,
        provider: 'expo-secure-store',
        isAvailable: await SecureStore.isAvailableAsync(),
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        itemCount: 0,
        totalSizeBytes: 0,
        provider: 'expo-secure-store',
        isAvailable: false,
      };
    }
  }

  /**
   * Store authentication data securely
   */
  async saveAuthData(authData: AuthenticationData): Promise<void> {
    try {
      await this.setObject(StorageKeys.AUTH_DATA, authData);
    } catch (error) {
      throw new Error(`Failed to save authentication data: ${error}`);
    }
  }

  /**
   * Retrieve stored authentication data
   */
  async getAuthData(): Promise<AuthenticationData | null> {
    try {
      return await this.getObject<AuthenticationData>(StorageKeys.AUTH_DATA);
    } catch (error) {
      console.error('Failed to retrieve authentication data:', error);
      return null;
    }
  }

  /**
   * Clear all authentication data
   */
  async clearAuthData(): Promise<void> {
    try {
      await Promise.all([
        this.removeItem(StorageKeys.AUTH_DATA),
        this.removeItem(StorageKeys.USER_PROFILE),
      ]);
    } catch (error) {
      throw new Error(`Failed to clear authentication data: ${error}`);
    }
  }

  /**
   * Store user profile information
   */
  async saveUserProfile(user: AuthenticatedUser): Promise<void> {
    try {
      await this.setObject(StorageKeys.USER_PROFILE, user);
    } catch (error) {
      throw new Error(`Failed to save user profile: ${error}`);
    }
  }

  /**
   * Retrieve stored user profile
   */
  async getUserProfile(): Promise<AuthenticatedUser | null> {
    try {
      return await this.getObject<AuthenticatedUser>(StorageKeys.USER_PROFILE);
    } catch (error) {
      console.error('Failed to retrieve user profile:', error);
      return null;
    }
  }

  /**
   * Check if authentication data exists and is valid
   */
  async hasValidAuthData(): Promise<boolean> {
    try {
      const authData = await this.getAuthData();
      if (!authData) {
        return false;
      }

      // Check if token is not expired
      const now = Date.now();
      return authData.expiresAt > now;
    } catch (error) {
      console.error('Failed to check auth data validity:', error);
      return false;
    }
  }

  /**
   * Get all known storage keys
   * Note: expo-secure-store doesn't provide a way to list all keys,
   * so we maintain a list of known keys
   */
  private async getAllKeys(): Promise<string[]> {
    return [
      StorageKeys.AUTH_DATA,
      StorageKeys.USER_PROFILE,
      StorageKeys.USER_PREFERENCES,
      StorageKeys.API_CONFIG,
      StorageKeys.APP_SETTINGS,
    ];
  }

  /**
   * Add prefix to storage key to avoid conflicts
   */
  private getPrefixedKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }
}
