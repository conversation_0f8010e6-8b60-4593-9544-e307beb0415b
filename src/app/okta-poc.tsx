/**
 * Okta Authentication Proof of Concept
 * 
 * Standalone implementation that completely bypasses all existing auth infrastructure.
 * Pure Expo + Okta integration following official documentation.
 */

import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri, useAuthRequest, useAutoDiscovery } from 'expo-auth-session';

// Complete auth session for web - required by Expo docs
WebBrowser.maybeCompleteAuthSession();

export default function OktaPoCScreen() {
  const [authResult, setAuthResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRedirectUri, setSelectedRedirectUri] = useState('expo_proxy_explicit');

  // Okta configuration from environment
  const oktaDomain = process.env.EXPO_PUBLIC_OKTA_ISSUER || 'https://integrator-5743111.okta.com/oauth2/default';
  const clientId = process.env.EXPO_PUBLIC_OKTA_CLIENT_ID || 'your-client-id-here';

  // Try different redirect URI formats to debug the issue
  const redirectUriOptions = {
    expo_proxy_explicit: 'https://auth.expo.dev/@charlesrmajor/learning-coach-community',
    native_scheme: 'learningcoachcommunity://callback',
    okta_native: 'com.okta.integrator-5743111:/callback',
  };

  // Use the explicit Expo proxy URI that matches your Okta config
  // const redirectUri = redirectUriOptions.expo_proxy_explicit;
  const redirectUri = makeRedirectUri({
        native: 'com.okta.integrator-5743111:/callback',
      });

  console.log('🔍 [Okta PoC] Using redirect URI:', redirectUri);

  console.log('🚀 [Okta PoC] Full config:', {
    oktaDomain,
    clientId: '0oauipzrsuO08tffD697' || clientId.substring(0, 8) + '...',
    redirectUri,
  });

  // Auto-discovery endpoint
  const discovery = useAutoDiscovery(oktaDomain);

  // Create auth request
  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId: '0oauipzrsuO08tffD697',
      scopes: ['openid', 'profile'],
      redirectUri: redirectUri,
    },
    discovery
  );

  // Handle authentication response
  useEffect(() => {
    if (response?.type === 'success') {
      console.log('✅ [Okta PoC] Authentication successful:', response);
      const { code } = response.params;
      setAuthResult({ 
        success: true, 
        code, 
        message: `Success! Received authorization code: ${code?.substring(0, 20)}...` 
      });
      setIsLoading(false);
      
      Alert.alert(
        '🎉 Authentication Success!', 
        `Authorization code received: ${code?.substring(0, 20)}...\n\nIn a real app, you would now exchange this code for access tokens.`,
        [{ text: 'Awesome!' }]
      );
    } else if (response?.type === 'error') {
      console.error('❌ [Okta PoC] Authentication error:', response.error);
      const errorMessage = response.error?.message || 'Authentication failed';
      setAuthResult({ 
        success: false, 
        error: errorMessage,
        message: `Error: ${errorMessage}` 
      });
      setIsLoading(false);
      
      Alert.alert('❌ Authentication Error', errorMessage);
    } else if (response?.type === 'cancel') {
      console.log('🚫 [Okta PoC] Authentication cancelled');
      setAuthResult({ 
        success: false, 
        error: 'cancelled',
        message: 'Authentication was cancelled by user' 
      });
      setIsLoading(false);
      
      Alert.alert('🚫 Authentication Cancelled', 'You cancelled the login process');
    }
  }, [response]);

  const handleLogin = async () => {
    if (!request) {
      Alert.alert('⚠️ Not Ready', 'Authentication request is not ready yet. Please wait a moment and try again.');
      return;
    }

    if (!discovery) {
      Alert.alert('⚠️ Not Ready', 'Okta discovery is still loading. Please wait a moment and try again.');
      return;
    }

    console.log('🔐 [Okta PoC] Starting authentication flow...');
    setIsLoading(true);
    setAuthResult(null);
    
    try {
      await promptAsync();
    } catch (error) {
      console.error('🔐 [Okta PoC] Login error:', error);
      setIsLoading(false);
      setAuthResult({ 
        success: false, 
        error: 'login_failed',
        message: 'Failed to start authentication process' 
      });
      Alert.alert('❌ Login Error', 'Failed to start authentication process');
    }
  };

  const getStatusInfo = () => {
    if (isLoading) return { color: '#F59E0B', text: '🔄 Authenticating...', ready: false };
    if (authResult?.success) return { color: '#10B981', text: '✅ Authentication Successful!', ready: true };
    if (authResult?.error) return { color: '#EF4444', text: `❌ ${authResult.message}`, ready: true };
    if (!discovery) return { color: '#6B7280', text: '⏳ Loading Okta configuration...', ready: false };
    if (!request) return { color: '#6B7280', text: '⏳ Preparing authentication...', ready: false };
    return { color: '#3B82F6', text: '🚀 Ready to authenticate!', ready: true };
  };

  const status = getStatusInfo();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>🔐 Okta Authentication PoC</Text>
          <Text style={styles.subtitle}>
            Standalone proof of concept following Expo's official documentation
          </Text>
        </View>

        <View style={styles.statusCard}>
          <Text style={[styles.statusText, { color: status.color }]}>
            {status.text}
          </Text>
        </View>

        <View style={styles.configCard}>
          <Text style={styles.cardTitle}>Configuration</Text>
          <View style={styles.configRow}>
            <Text style={styles.configLabel}>Okta Domain:</Text>
            <Text style={styles.configValue}>{oktaDomain}</Text>
          </View>
          <View style={styles.configRow}>
            <Text style={styles.configLabel}>Client ID:</Text>
            <Text style={styles.configValue}>{clientId.substring(0, 12)}...</Text>
          </View>
          <View style={styles.configRow}>
            <Text style={styles.configLabel}>Redirect URI:</Text>
            <Text style={styles.configValue}>{redirectUri}</Text>
          </View>
          <View style={styles.configRow}>
            <Text style={styles.configLabel}>Discovery:</Text>
            <Text style={[styles.configValue, { color: discovery ? '#10B981' : '#F59E0B' }]}>
              {discovery ? '✅ Loaded' : '⏳ Loading...'}
            </Text>
          </View>
          <View style={styles.configRow}>
            <Text style={styles.configLabel}>Auth Request:</Text>
            <Text style={[styles.configValue, { color: request ? '#10B981' : '#F59E0B' }]}>
              {request ? '✅ Ready' : '⏳ Preparing...'}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={[
            styles.loginButton, 
            (!status.ready || isLoading) && styles.loginButtonDisabled
          ]}
          onPress={handleLogin}
          disabled={!status.ready || isLoading}
        >
          <Text style={styles.loginButtonText}>
            {isLoading ? '🔄 Authenticating...' : '🚀 Login with Okta'}
          </Text>
        </TouchableOpacity>

        {authResult && (
          <View style={[
            styles.resultCard,
            { backgroundColor: authResult.success ? '#ECFDF5' : '#FEF2F2' }
          ]}>
            <Text style={[
              styles.resultTitle,
              { color: authResult.success ? '#065F46' : '#991B1B' }
            ]}>
              {authResult.success ? '🎉 Success!' : '❌ Error'}
            </Text>
            <Text style={[
              styles.resultMessage,
              { color: authResult.success ? '#047857' : '#DC2626' }
            ]}>
              {authResult.message}
            </Text>
            {authResult.success && (
              <Text style={styles.nextSteps}>
                Next steps: Exchange this authorization code for access tokens using your backend server.
              </Text>
            )}
          </View>
        )}

        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>How This Works</Text>
          <Text style={styles.infoText}>
            1. 🔍 Auto-discovers Okta endpoints using your domain
          </Text>
          <Text style={styles.infoText}>
            2. 🛠️ Creates authentication request with PKCE
          </Text>
          <Text style={styles.infoText}>
            3. 🌐 Uses Expo's auth proxy for redirect handling
          </Text>
          <Text style={styles.infoText}>
            4. 🔐 Opens Okta login in secure browser
          </Text>
          <Text style={styles.infoText}>
            5. ✅ Returns authorization code for token exchange
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1E293B',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 22,
  },
  statusCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  statusText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  configCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 12,
  },
  configRow: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'flex-start',
  },
  configLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#475569',
    width: 100,
    flexShrink: 0,
  },
  configValue: {
    fontSize: 14,
    color: '#1E293B',
    flex: 1,
    fontFamily: 'monospace',
  },
  loginButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  loginButtonDisabled: {
    backgroundColor: '#94A3B8',
    shadowOpacity: 0,
    elevation: 0,
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  resultCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  resultMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  nextSteps: {
    fontSize: 12,
    color: '#047857',
    fontStyle: 'italic',
  },
  infoCard: {
    backgroundColor: '#EFF6FF',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#DBEAFE',
  },
  infoText: {
    fontSize: 14,
    color: '#1E40AF',
    marginBottom: 6,
    lineHeight: 20,
  },
});
