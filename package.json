{"name": "learning-coach-community", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "prepare": "husky install", "commit": "npx cz", "test": "jest --watchAll"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["./jest/setup.ts"], "collectCoverage": true, "collectCoverageFrom": ["**/*.{ts,tsx,js,jsx}", "!**/coverage/**", "!**/node_modules/**", "!**/babel.config.js", "!**/expo-env.d.ts", "!**/.expo/**"], "globals": {"ts-jest": {"tsconfig": "./jest/tsconfig.json"}}, "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?|expo(nent)?|@expo(nent)?|expo-router|react-navigation|@react-navigation/.*|react-redux))"]}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@okta/okta-auth-js": "^7.14.0", "@okta/okta-react-native": "^2.17.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.4.5", "@reduxjs/toolkit": "^2.8.2", "@types/jest": "^30.0.0", "axios": "^1.11.0", "axios-mock-adapter": "^2.1.0", "expo": "~53.0.20", "expo-auth-session": "~6.2.1", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "jest": "~29.7.0", "jest-expo": "~53.0.9", "nativewind": "^4.1.23", "react": "^19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "expo-crypto": "~14.1.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.3", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "commitizen": "^4.3.1", "eslint": "^9.32.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-native": "^5.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.5.11", "react-test-renderer": "^19.0.0", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "private": true}