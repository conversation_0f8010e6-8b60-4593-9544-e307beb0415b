/**
 * Secure Storage Service
 * 
 * Business logic layer for secure storage operations.
 * Provides validation, error handling, and business rules for secure data storage.
 */

import { ISecureStorageRepository } from '@/repositories/interfaces';
import { 
  StorageResult, 
  StorageInfo, 
  StorageEvent, 
  StorageEventData,
  BatchStorageOperation,
  BatchStorageResult,
  StorageOperation 
} from '@/types/storage';

/**
 * Secure storage service providing business logic for storage operations
 */
export class SecureStorageService {
  private eventListeners: Map<StorageEvent, ((data: StorageEventData) => void)[]> = new Map();

  constructor(
    private storageRepository: ISecureStorageRepository
  ) {
    // Initialize event listeners map
    Object.values(StorageEvent).forEach(event => {
      this.eventListeners.set(event, []);
    });
  }

  /**
   * Store secure data with business validation
   */
  async storeSecureData<T>(key: string, data: T, options?: { expirationMs?: number }): Promise<StorageResult<void>> {
    const startTime = Date.now();
    
    try {
      // Business validation
      this.validateStorageKey(key);
      this.validateStorageData(data);

      // Store the data
      await this.storageRepository.setObject(key, data);

      const duration = Date.now() - startTime;
      
      this.emitEvent(StorageEvent.ITEM_SET, {
        event: StorageEvent.ITEM_SET,
        key,
        timestamp: Date.now(),
        metadata: { duration }
      });

      return { 
        success: true,
        metadata: {
          duration,
          key,
          operation: StorageOperation.SET
        }
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Storage operation failed';
      const duration = Date.now() - startTime;
      
      this.emitEvent(StorageEvent.STORAGE_ERROR, {
        event: StorageEvent.STORAGE_ERROR,
        key,
        error: errorMessage,
        timestamp: Date.now(),
        metadata: { duration }
      });

      return {
        success: false,
        error: errorMessage,
        metadata: {
          duration,
          key,
          operation: StorageOperation.SET
        }
      };
    }
  }

  /**
   * Retrieve secure data with business validation
   */
  async getSecureData<T>(key: string): Promise<StorageResult<T>> {
    const startTime = Date.now();
    
    try {
      this.validateStorageKey(key);

      const data = await this.storageRepository.getObject<T>(key);
      const duration = Date.now() - startTime;
      
      this.emitEvent(StorageEvent.ITEM_GET, {
        event: StorageEvent.ITEM_GET,
        key,
        timestamp: Date.now(),
        metadata: { duration, found: data !== null }
      });

      return { 
        success: true, 
        data: data || undefined,
        metadata: {
          duration,
          key,
          operation: StorageOperation.GET
        }
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Retrieval operation failed';
      const duration = Date.now() - startTime;
      
      this.emitEvent(StorageEvent.STORAGE_ERROR, {
        event: StorageEvent.STORAGE_ERROR,
        key,
        error: errorMessage,
        timestamp: Date.now(),
        metadata: { duration }
      });

      return {
        success: false,
        error: errorMessage,
        metadata: {
          duration,
          key,
          operation: StorageOperation.GET
        }
      };
    }
  }

  /**
   * Remove secure data with business validation
   */
  async removeSecureData(key: string): Promise<StorageResult<void>> {
    const startTime = Date.now();
    
    try {
      this.validateStorageKey(key);

      await this.storageRepository.removeObject(key);
      const duration = Date.now() - startTime;
      
      this.emitEvent(StorageEvent.ITEM_REMOVED, {
        event: StorageEvent.ITEM_REMOVED,
        key,
        timestamp: Date.now(),
        metadata: { duration }
      });

      return { 
        success: true,
        metadata: {
          duration,
          key,
          operation: StorageOperation.REMOVE
        }
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Removal operation failed';
      const duration = Date.now() - startTime;
      
      this.emitEvent(StorageEvent.STORAGE_ERROR, {
        event: StorageEvent.STORAGE_ERROR,
        key,
        error: errorMessage,
        timestamp: Date.now(),
        metadata: { duration }
      });

      return {
        success: false,
        error: errorMessage,
        metadata: {
          duration,
          key,
          operation: StorageOperation.REMOVE
        }
      };
    }
  }

  /**
   * Clear all stored data
   */
  async clearAllData(): Promise<StorageResult<void>> {
    const startTime = Date.now();
    
    try {
      await this.storageRepository.clear();
      const duration = Date.now() - startTime;
      
      this.emitEvent(StorageEvent.STORAGE_CLEARED, {
        event: StorageEvent.STORAGE_CLEARED,
        timestamp: Date.now(),
        metadata: { duration }
      });

      return { 
        success: true,
        metadata: {
          duration,
          operation: StorageOperation.CLEAR
        }
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Clear operation failed';
      const duration = Date.now() - startTime;
      
      this.emitEvent(StorageEvent.STORAGE_ERROR, {
        event: StorageEvent.STORAGE_ERROR,
        error: errorMessage,
        timestamp: Date.now(),
        metadata: { duration }
      });

      return {
        success: false,
        error: errorMessage,
        metadata: {
          duration,
          operation: StorageOperation.CLEAR
        }
      };
    }
  }

  /**
   * Check if data exists for a key
   */
  async hasData(key: string): Promise<StorageResult<boolean>> {
    const startTime = Date.now();
    
    try {
      this.validateStorageKey(key);

      const exists = await this.storageRepository.exists(key);
      const duration = Date.now() - startTime;

      return { 
        success: true, 
        data: exists,
        metadata: {
          duration,
          key,
          operation: StorageOperation.EXISTS
        }
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Existence check failed';
      const duration = Date.now() - startTime;
      
      this.emitEvent(StorageEvent.STORAGE_ERROR, {
        event: StorageEvent.STORAGE_ERROR,
        key,
        error: errorMessage,
        timestamp: Date.now(),
        metadata: { duration }
      });

      return {
        success: false,
        error: errorMessage,
        metadata: {
          duration,
          key,
          operation: StorageOperation.EXISTS
        }
      };
    }
  }

  /**
   * Get storage information and statistics
   */
  async getStorageInfo(): Promise<StorageResult<StorageInfo>> {
    try {
      const info = await this.storageRepository.getStorageInfo();
      return { success: true, data: info };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get storage info';
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Store multiple items with validation
   */
  async storeMultiple<T>(items: Array<{ key: string; data: T }>): Promise<BatchStorageResult> {
    const results: StorageResult[] = [];
    let allSuccessful = true;

    for (const item of items) {
      const result = await this.storeSecureData(item.key, item.data);
      results.push(result);
      if (!result.success) {
        allSuccessful = false;
      }
    }

    return {
      success: allSuccessful,
      results,
      error: allSuccessful ? undefined : 'Some storage operations failed'
    };
  }

  /**
   * Retrieve multiple items with validation
   */
  async getMultiple<T>(keys: string[]): Promise<StorageResult<Map<string, T | null>>> {
    try {
      const results = new Map<string, T | null>();
      
      for (const key of keys) {
        const result = await this.getSecureData<T>(key);
        results.set(key, result.success ? result.data || null : null);
      }

      return { success: true, data: results };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Multiple retrieval failed';
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Add event listener for storage events
   */
  addEventListener(event: StorageEvent, listener: (data: StorageEventData) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.push(listener);
    }
  }

  /**
   * Remove event listener for storage events
   */
  removeEventListener(event: StorageEvent, listener: (data: StorageEventData) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Validate storage key
   */
  private validateStorageKey(key: string): void {
    if (!key || typeof key !== 'string') {
      throw new Error('Storage key must be a non-empty string');
    }

    if (key.trim().length === 0) {
      throw new Error('Storage key cannot be empty or whitespace only');
    }

    if (key.length > 250) {
      throw new Error('Storage key cannot exceed 250 characters');
    }

    // Check for invalid characters
    if (!/^[a-zA-Z0-9_.-]+$/.test(key)) {
      throw new Error('Storage key can only contain alphanumeric characters, underscores, dots, and hyphens');
    }
  }

  /**
   * Validate storage data
   */
  private validateStorageData<T>(data: T): void {
    if (data === null || data === undefined) {
      throw new Error('Cannot store null or undefined data');
    }

    // Check if data can be serialized
    try {
      JSON.stringify(data);
    } catch (error) {
      throw new Error('Data must be JSON serializable');
    }

    // Check data size (approximate)
    const dataSize = new Blob([JSON.stringify(data)]).size;
    const maxSize = 1024 * 1024; // 1MB limit
    
    if (dataSize > maxSize) {
      throw new Error(`Data size (${dataSize} bytes) exceeds maximum allowed size (${maxSize} bytes)`);
    }
  }

  /**
   * Emit storage event to listeners
   */
  private emitEvent(event: StorageEvent, data: StorageEventData): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in storage event listener for ${event}:`, error);
        }
      });
    }
  }
}
