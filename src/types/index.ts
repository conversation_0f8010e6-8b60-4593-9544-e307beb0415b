/**
 * Shared types for API services and Clean Architecture
 */

// Re-export authentication types
export * from './auth';
export * from './storage';

// Re-export repository interfaces
export * from '../repositories/interfaces';
export * from '../repositories/auth/interfaces';

/**
 * Storage interface for dependency injection (legacy)
 * @deprecated Use ISecureStorageRepository instead
 */
export interface IStorageRepository {
	getItem(key: string): Promise<string | null>;
	setItem(key: string, value: string): Promise<void>;
	removeItem(key: string): Promise<void>;
}

/**
 * API Configuration interface
 */
export interface IApiConfigService {
	loadApiConfig(): Promise<{ apiKey: string | null; apiBaseUrl: string | null }>;
	saveApiKey(apiKey: string): Promise<void>;
	saveApiBaseUrl(url: string): Promise<void>;
}

/**
 * API Client configuration options
 */
export interface ApiClientOptions {
	timeout?: number;
	retryAttempts?: number;
	baseUrl?: string; // Override config baseUrl
}

/**
 * API Response wrapper
 */
export interface ApiResponse<T = any> {
	data: T;
	success: boolean;
	message?: string;
}
