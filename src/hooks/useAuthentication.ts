/**
 * Authentication Hook
 * 
 * React hook providing authentication state and operations.
 * Integrates with the authentication service to provide a clean API for components.
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import { ServiceFactory } from '@/services/serviceFactory';
import { AuthenticationService } from '@/services/authenticationService';
import { 
  AuthenticationState, 
  AuthenticatedUser, 
  AuthEvent, 
  AuthEventData,
  TokenRefreshResult 
} from '@/types/auth';

/**
 * Authentication hook return type
 */
export interface UseAuthenticationReturn {
  // State
  isAuthenticated: boolean;
  user: AuthenticatedUser | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  signIn: () => Promise<boolean>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<boolean>;
  clearError: () => void;
  
  // Token operations
  getAccessToken: () => Promise<string | null>;
  getIdToken: () => Promise<string | null>;
  
  // Session management
  isSessionValid: () => Promise<boolean>;
  checkAuthState: () => Promise<void>;
}

/**
 * Hook options
 */
export interface UseAuthenticationOptions {
  /** Whether to automatically check auth state on mount */
  autoCheck?: boolean;
  /** Whether to automatically refresh tokens */
  autoRefresh?: boolean;
  /** Refresh buffer time in minutes */
  refreshBufferMinutes?: number;
  /** Polling interval for auth state checks in milliseconds */
  pollingInterval?: number;
}

/**
 * Authentication hook providing auth state and operations
 */
export function useAuthentication(options: UseAuthenticationOptions = {}): UseAuthenticationReturn {
  const {
    autoCheck = true,
    autoRefresh = true,
    refreshBufferMinutes = 5,
    pollingInterval = 60000, // 1 minute
  } = options;

  // State
  const [authState, setAuthState] = useState<AuthenticationState>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
    error: null,
  });

  // Service reference
  const authServiceRef = useRef<AuthenticationService | null>(null);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get auth service instance
  const getAuthService = useCallback((): AuthenticationService => {
    if (!authServiceRef.current) {
      authServiceRef.current = ServiceFactory.createAuthenticationService();
    }
    return authServiceRef.current;
  }, []);

  // Load authentication state
  const loadAuthState = useCallback(async (): Promise<void> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const authService = getAuthService();
      const state = await authService.getCurrentAuthState();
      
      setAuthState(state);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load auth state';
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: errorMessage,
      });
    }
  }, [getAuthService]);

  // Sign in
  const signIn = useCallback(async (): Promise<boolean> => {
    try {
      console.log('🔐 [useAuthentication] Starting sign-in process');
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const authService = getAuthService();
      console.log('🔐 [useAuthentication] Calling authService.signIn()');
      const result = await authService.signIn();
      console.log('🔐 [useAuthentication] authService.signIn() result:', result);
      
      setAuthState(result);
      return result.isAuthenticated;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return false;
    }
  }, [getAuthService]);

  // Sign out
  const signOut = useCallback(async (): Promise<void> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const authService = getAuthService();
      await authService.signOut();
      
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign out failed';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, [getAuthService]);

  // Refresh authentication
  const refreshAuth = useCallback(async (): Promise<boolean> => {
    try {
      const authService = getAuthService();
      const result: TokenRefreshResult = await authService.refreshAuthIfNeeded(refreshBufferMinutes);
      
      if (result.success) {
        // Reload auth state to get updated information
        await loadAuthState();
        return true;
      } else {
        console.warn('Token refresh failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    }
  }, [getAuthService, refreshBufferMinutes, loadAuthState]);

  // Clear error
  const clearError = useCallback((): void => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  // Get access token
  const getAccessToken = useCallback(async (): Promise<string | null> => {
    try {
      const authService = getAuthService();
      return await authService.getAccessToken();
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }, [getAuthService]);

  // Get ID token
  const getIdToken = useCallback(async (): Promise<string | null> => {
    try {
      const authService = getAuthService();
      return await authService.getIdToken();
    } catch (error) {
      console.error('Failed to get ID token:', error);
      return null;
    }
  }, [getAuthService]);

  // Check if session is valid
  const isSessionValid = useCallback(async (): Promise<boolean> => {
    try {
      const authService = getAuthService();
      return await authService.isSessionValid();
    } catch (error) {
      console.error('Failed to check session validity:', error);
      return false;
    }
  }, [getAuthService]);

  // Check auth state (public method)
  const checkAuthState = useCallback(async (): Promise<void> => {
    await loadAuthState();
  }, [loadAuthState]);

  // Set up automatic token refresh
  const setupAutoRefresh = useCallback((): void => {
    if (!autoRefresh) return;

    const scheduleRefresh = async (): Promise<void> => {
      if (authState.isAuthenticated) {
        await refreshAuth();
      }
      
      // Schedule next refresh
      refreshTimeoutRef.current = setTimeout(scheduleRefresh, refreshBufferMinutes * 60 * 1000);
    };

    // Clear existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Start refresh cycle
    refreshTimeoutRef.current = setTimeout(scheduleRefresh, refreshBufferMinutes * 60 * 1000);
  }, [autoRefresh, authState.isAuthenticated, refreshAuth, refreshBufferMinutes]);

  // Set up polling for auth state
  const setupPolling = useCallback((): void => {
    if (!pollingInterval) return;

    const poll = async (): Promise<void> => {
      if (authState.isAuthenticated) {
        const isValid = await isSessionValid();
        if (!isValid) {
          await loadAuthState();
        }
      }
      
      // Schedule next poll
      pollingTimeoutRef.current = setTimeout(poll, pollingInterval);
    };

    // Clear existing timeout
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current);
    }

    // Start polling
    pollingTimeoutRef.current = setTimeout(poll, pollingInterval);
  }, [pollingInterval, authState.isAuthenticated, isSessionValid, loadAuthState]);

  // Set up auth event listeners
  useEffect(() => {
    const authService = getAuthService();

    const handleAuthEvent = (data: AuthEventData): void => {
      switch (data.event) {
        case AuthEvent.SESSION_EXPIRED:
          setAuthState({
            isAuthenticated: false,
            user: null,
            isLoading: false,
            error: 'Session expired',
          });
          break;
        case AuthEvent.TOKEN_REFRESH_FAILURE:
          console.warn('Token refresh failed:', data.error);
          break;
      }
    };

    // Add event listeners
    authService.addEventListener(AuthEvent.SESSION_EXPIRED, handleAuthEvent);
    authService.addEventListener(AuthEvent.TOKEN_REFRESH_FAILURE, handleAuthEvent);

    return () => {
      // Remove event listeners
      authService.removeEventListener(AuthEvent.SESSION_EXPIRED, handleAuthEvent);
      authService.removeEventListener(AuthEvent.TOKEN_REFRESH_FAILURE, handleAuthEvent);
    };
  }, [getAuthService]);

  // Initialize on mount
  useEffect(() => {
    if (autoCheck) {
      loadAuthState();
    }
  }, [autoCheck, loadAuthState]);

  // Set up auto refresh and polling
  useEffect(() => {
    setupAutoRefresh();
    setupPolling();

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
      }
    };
  }, [setupAutoRefresh, setupPolling]);

  return {
    // State
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    isLoading: authState.isLoading,
    error: authState.error,
    
    // Actions
    signIn,
    signOut,
    refreshAuth,
    clearError,
    
    // Token operations
    getAccessToken,
    getIdToken,
    
    // Session management
    isSessionValid,
    checkAuthState,
  };
}
