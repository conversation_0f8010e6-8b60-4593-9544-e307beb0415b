/**
 * Okta Authentication Hook
 * 
 * React hook that follows Expo's official documentation pattern for Okta integration.
 * Provides a simple interface for authentication in React components.
 */

import { useEffect, useState } from 'react';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri, useAuthRequest, useAutoDiscovery, exchangeCodeAsync } from 'expo-auth-session';
import { AuthenticationData } from '@/types/auth';

// Complete auth session for web
WebBrowser.maybeCompleteAuthSession();

interface UseOktaAuthConfig {
  issuer?: string;
  clientId?: string;
  scopes?: string[];
  redirectUri?: string;
}

interface UseOktaAuthReturn {
  request: any;
  response: any;
  promptAsync: () => Promise<any>;
  isLoading: boolean;
  authData: AuthenticationData | null;
  error: string | null;
  signIn: () => Promise<void>;
  signOut: () => void;
  isAuthenticated: boolean;
}

/**
 * Hook for Okta authentication following Expo's official pattern
 */
export function useOktaAuth(config?: UseOktaAuthConfig): UseOktaAuthReturn {
  const [authData, setAuthData] = useState<AuthenticationData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Configuration
  const issuer = config?.issuer || process.env.EXPO_PUBLIC_OKTA_ISSUER!;
  const clientId = config?.clientId || process.env.EXPO_PUBLIC_OKTA_CLIENT_ID!;
  const scopes = config?.scopes || ['openid', 'profile', 'email', 'offline_access'];
  
  const redirectUri = config?.redirectUri || makeRedirectUri({
    native: 'https://auth.expo.dev/@charlesrmajor/learning-coach-community',
    useProxy: true,
  });

  // Expo auth session hooks
  const discovery = useAutoDiscovery(issuer);
  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId,
      scopes,
      redirectUri,
    },
    discovery
  );

  // Handle authentication response
  useEffect(() => {
    if (response?.type === 'success') {
      handleAuthSuccess(response);
    } else if (response?.type === 'error') {
      setError(`Authentication error: ${response.error?.message || 'Unknown error'}`);
      setIsLoading(false);
    } else if (response?.type === 'cancel') {
      setError('Authentication was cancelled');
      setIsLoading(false);
    }
  }, [response]);

  const handleAuthSuccess = async (authResponse: any) => {
    try {
      setIsLoading(true);
      setError(null);

      const { code } = authResponse.params;
      
      if (!code) {
        throw new Error('No authorization code received');
      }

      // Exchange code for tokens
      const tokenResult = await exchangeCodeAsync(
        {
          clientId,
          code,
          redirectUri,
        },
        discovery
      );

      const authenticationData: AuthenticationData = {
        accessToken: tokenResult.accessToken,
        refreshToken: tokenResult.refreshToken,
        idToken: tokenResult.idToken,
        expiresAt: Date.now() + (tokenResult.expiresIn ? tokenResult.expiresIn * 1000 : 3600 * 1000),
        tokenType: 'Bearer',
        scope: scopes,
      };

      setAuthData(authenticationData);
      setIsLoading(false);
    } catch (err) {
      console.error('Token exchange failed:', err);
      setError(`Token exchange failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setIsLoading(false);
    }
  };

  const signIn = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await promptAsync();
    } catch (err) {
      console.error('Sign in failed:', err);
      setError(`Sign in failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setIsLoading(false);
    }
  };

  const signOut = () => {
    setAuthData(null);
    setError(null);
  };

  const isAuthenticated = authData !== null && Date.now() < authData.expiresAt;

  return {
    request,
    response,
    promptAsync,
    isLoading,
    authData,
    error,
    signIn,
    signOut,
    isAuthenticated,
  };
}

/**
 * Simple hook for checking authentication status
 */
export function useAuthStatus() {
  const { isAuthenticated, authData, isLoading } = useOktaAuth();
  
  return {
    isAuthenticated,
    user: authData,
    isLoading,
  };
}
