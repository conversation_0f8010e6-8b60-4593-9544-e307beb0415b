# Okta Application Setup Guide

**Status**: Ready for Configuration  
**Date**: 2025-08-20  
**For**: Learning Coach Community App with Expo Auth Proxy

## 🎯 Overview

This guide walks you through setting up your Okta application to work with the Clean Architecture authentication system using Expo's auth proxy.

## 📋 Prerequisites

- Okta Developer Account (free at https://developer.okta.com)
- Admin access to your Okta organization
- The Learning Coach Community app configured with Clean Architecture

## 🔧 Step-by-Step Configuration

### Step 1: Create New Application

1. **Log into Okta Admin Console**
   - Go to your Okta organization (e.g., `https://dev-123456.okta.com`)
   - Sign in with your admin credentials

2. **Navigate to Applications**
   - Click **Applications** in the left sidebar
   - Click **Create App Integration**

3. **Choose Application Type**
   - Select **OIDC - OpenID Connect**
   - Select **Single-Page Application (SPA)**
   - Click **Next**

### Step 2: Configure Application Settings

#### General Settings
- **App integration name**: `Learning Coach Community`
- **Logo**: Upload your app logo (optional)

#### Sign-in redirect URIs
Add these redirect URIs (one per line):
```
https://auth.expo.dev/@charlesrmajor/learning-coach-community
http://localhost:8081/callback
http://localhost:8082/callback
```

#### Sign-out redirect URIs
Add these sign-out URIs (one per line):
```
https://auth.expo.dev/@charlesrmajor/learning-coach-community
http://localhost:8081
http://localhost:8082
```

#### Controlled access
- Select **Allow everyone in your organization to access**
- Or configure specific groups as needed

### Step 3: Advanced Settings

#### Grant Types
Ensure these are enabled:
- ✅ **Authorization Code**
- ✅ **Refresh Token**
- ✅ **Implicit (Hybrid)** - for web compatibility

#### Client Authentication
- Select **Use PKCE (for public clients)**
- This is required for mobile apps and recommended for SPAs

#### Refresh Token Behavior
- **Refresh token behavior**: Rotate token after every use
- **Grace period for token rotation**: 30 seconds

### Step 4: API Scopes

#### OpenID Connect Scopes
Ensure these scopes are granted:
- ✅ **openid** - Required for OIDC
- ✅ **profile** - User profile information
- ✅ **email** - User email address
- ✅ **offline_access** - Refresh tokens (optional)

#### Custom Scopes
Add any custom scopes your app needs (optional)

### Step 5: Trusted Origins (CORS)

1. **Navigate to Security > API > Trusted Origins**
2. **Add Trusted Origin** for each environment:

**Development Origins:**
- **Origin URL**: `http://localhost:8081`
- **Type**: CORS ✅, Redirect ✅
- **Origin URL**: `http://localhost:8082`
- **Type**: CORS ✅, Redirect ✅

**Expo Proxy Origin:**
- **Origin URL**: `https://auth.expo.dev`
- **Type**: CORS ✅, Redirect ✅

### Step 6: Get Configuration Values

After creating the application, note these values:

1. **Client ID**: Found on the application's General tab
2. **Okta Domain**: Your organization URL (e.g., `dev-123456.okta.com`)
3. **Issuer URL**: Usually `https://your-domain.okta.com/oauth2/default`

## 📝 Environment Configuration

Create `.env.local` in your project root:

```bash
# Okta Configuration
EXPO_PUBLIC_OKTA_ISSUER=https://integrator-5743111.okta.com/oauth2/default
EXPO_PUBLIC_OKTA_CLIENT_ID=your-actual-client-id-here
EXPO_PUBLIC_APP_SCHEME=learningcoachcommunity
```

**Replace with your actual values:**
- `integrator-5743111` → your Okta domain (already correct)
- `your-actual-client-id-here` → your actual Client ID from Okta

## 🧪 Testing the Configuration

### Test Authentication Flow

1. **Start your app**:
   ```bash
   npx expo start
   ```

2. **Test on different platforms**:
   - Press `i` for iOS simulator
   - Press `w` for web browser
   - Scan QR code for physical device

3. **Verify authentication**:
   - App should no longer show "Okta Client ID is required" error
   - Authentication flow should redirect to Okta login
   - After login, should redirect back to your app

### Expected Flow

1. **User clicks "Sign In"**
2. **App redirects to Okta** (via Expo proxy)
3. **User enters credentials** on Okta login page
4. **Okta redirects back** to `https://auth.expo.dev/@charlesrmajor/learning-coach-community`
5. **Expo proxy redirects** back to your app with auth code
6. **App exchanges code** for tokens
7. **User is authenticated** and can access protected features

## 🔍 Troubleshooting

### Common Issues

**"Invalid redirect URI"**
- Ensure all redirect URIs are added to Okta application
- Check for typos in the Expo proxy URL
- Verify CORS trusted origins are configured

**"Client authentication failed"**
- Ensure PKCE is enabled in Okta application
- Verify Client ID is correct in environment variables

**"Invalid scope"**
- Ensure required scopes (openid, profile, email) are granted
- Check if custom scopes are properly configured

**CORS errors on web**
- Add localhost origins to Trusted Origins
- Ensure both CORS and Redirect are checked

### Debug Steps

1. **Check environment variables**:
   ```bash
   npx expo start --clear
   # Look for "Okta Client ID is required" errors
   ```

2. **Verify Okta configuration**:
   - Double-check redirect URIs
   - Confirm PKCE is enabled
   - Verify scopes are granted

3. **Test network requests**:
   - Open browser dev tools
   - Check for CORS errors
   - Verify redirect URLs in network tab

## 🚀 Production Considerations

### Production Redirect URIs

For production deployment, you'll need to add your production URLs:

```
https://yourapp.com/callback
https://yourapp.com
```

### Security Best Practices

1. **Use HTTPS** for all production redirect URIs
2. **Limit trusted origins** to only necessary domains
3. **Configure appropriate token lifetimes**
4. **Enable MFA** for enhanced security
5. **Monitor authentication logs** in Okta

### Environment-Specific Configuration

Consider using different Okta applications for:
- **Development**: `dev-yourorg.okta.com`
- **Staging**: `staging-yourorg.okta.com`
- **Production**: `yourorg.okta.com`

## 📚 Additional Resources

- [Okta SPA Authentication Guide](https://developer.okta.com/docs/guides/sign-into-spa-redirect/)
- [Expo AuthSession Documentation](https://docs.expo.dev/guides/authentication/)
- [PKCE Flow Specification](https://tools.ietf.org/html/rfc7636)
- [Clean Architecture Documentation](./clean-architecture-secure-storage-auth.md)

---

**Next Steps**: After completing this setup, your app will have full authentication capabilities across mobile and web platforms using the Clean Architecture pattern! 🎉
