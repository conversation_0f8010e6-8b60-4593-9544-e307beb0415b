/**
 * Okta Web Authentication Provider
 *
 * Implementation of authentication for web platforms using @okta/okta-auth-js.
 * Handles OIDC authentication with PKCE flow and leverages Okta's session management.
 */

import { OktaAuth, AccessToken, IDToken } from '@okta/okta-auth-js';
import { IOktaAuthProvider } from './interfaces';
import { AuthenticationData, AuthenticatedUser, AuthConfig, AuthResult } from '@/types/auth';

/**
 * Okta implementation of authentication using @okta/okta-auth-js
 */
export class OktaWebAuthProvider implements IOktaAuthProvider {
  private oktaAuth: OktaAuth;

  constructor(config?: Partial<AuthConfig>) {
    // Use environment variables with config overrides
    const issuer = config?.issuer || process.env.EXPO_PUBLIC_OKTA_ISSUER!;
    const clientId = config?.clientId || process.env.EXPO_PUBLIC_OKTA_CLIENT_ID!;

    // Determine redirect URI based on environment and config
    let redirectUri: string;
    if (config?.redirectUri) {
      redirectUri = config.redirectUri;
    } else if (__DEV__) {
      // In development, use local callback
      redirectUri = `${window.location.origin}/callback`;
    } else {
      // In production, use Expo auth proxy
      redirectUri = "https://auth.expo.dev/@charlesrmajor/learning-coach-community";
    }
    const scopes = config?.scopes || ['openid', 'profile', 'email', 'offline_access'];

    if (!issuer) {
      throw new Error('Okta Issuer is required for web authentication');
    }

    if (!clientId) {
      throw new Error('Okta Client ID is required for web authentication');
    }

    // Initialize Okta Auth client
    this.oktaAuth = new OktaAuth({
      issuer,
      clientId,
      redirectUri,
      scopes,
      pkce: true,
      responseMode: 'fragment',
      responseType: 'code',
      restoreOriginalUri: async (_oktaAuth, originalUri) => {
        // Restore the original URI after authentication
        window.location.replace(originalUri || '/');
      },
      tokenManager: {
        autoRenew: true,
        autoRemove: true,
        storage: 'memory', // Use memory storage for security
      },
      ...config?.additionalParameters,
    });

    // Token manager is accessed via this.oktaAuth.tokenManager

    // Set up token renewal
    this.setupTokenRenewal();
  }

  /**
   * Initiate sign-in process using Okta redirect
   */
  async signIn(): Promise<AuthenticationData> {
    try {
      // Check if we're already authenticated
      const isAuthenticated = await this.isAuthenticated();
      if (isAuthenticated) {
        return await this.getCurrentTokens();
      }

      // Redirect to Okta for authentication
      await this.signInWithRedirect();
      
      // This line won't be reached due to redirect
      throw new Error('Redirect in progress');
    } catch (error) {
      console.error('Sign in failed:', error);
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    try {
      await this.oktaAuth.signOut();
    } catch (error) {
      console.error('Sign out failed:', error);
      throw new Error(`Sign out failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Refresh the current authentication token
   */
  async refreshToken(): Promise<AuthenticationData> {
    try {
      await this.oktaAuth.tokenManager.renew('accessToken');

      // Also renew ID token if available
      try {
        await this.oktaAuth.tokenManager.renew('idToken');
      } catch (idTokenError) {
        console.warn('Failed to renew ID token:', idTokenError);
      }

      return await this.getCurrentTokens();
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw new Error(`Token refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      return await this.oktaAuth.isAuthenticated();
    } catch (error) {
      console.error('Authentication check failed:', error);
      return false;
    }
  }

  /**
   * Get current authenticated user information
   */
  async getCurrentUser(): Promise<AuthenticatedUser | null> {
    try {
      const user = await this.oktaAuth.getUser();
      if (!user) {
        return null;
      }

      return {
        id: user.sub!,
        email: user.email!,
        name: user.name || `${user.given_name || ''} ${user.family_name || ''}`.trim(),
        roles: Array.isArray(user.groups) ? user.groups as string[] : typeof user.groups === 'string' ? [user.groups] : [],
        tenant: typeof user.tenant === 'string' ? user.tenant : undefined,
        groups: Array.isArray(user.groups) ? user.groups as string[] : typeof user.groups === 'string' ? [user.groups] : [],
        attributes: {
          given_name: user.given_name,
          family_name: user.family_name,
          preferred_username: user.preferred_username,
          locale: user.locale,
          zoneinfo: user.zoneinfo,
        },
      };
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  /**
   * Get current access token
   */
  async getAccessToken(): Promise<string | null> {
    try {
      const accessToken = await this.oktaAuth.tokenManager.get('accessToken') as AccessToken;
      return accessToken?.accessToken || null;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  /**
   * Get current ID token
   */
  async getIdToken(): Promise<string | null> {
    try {
      const idToken = await this.oktaAuth.tokenManager.get('idToken') as IDToken;
      return idToken?.idToken || null;
    } catch (error) {
      console.error('Failed to get ID token:', error);
      return null;
    }
  }

  /**
   * Check if current token is expired
   */
  async isTokenExpired(): Promise<boolean> {
    try {
      const accessToken = await this.oktaAuth.tokenManager.get('accessToken') as AccessToken;
      if (!accessToken) {
        return true;
      }

      return Date.now() >= (accessToken.expiresAt * 1000);
    } catch (error) {
      console.error('Failed to check token expiration:', error);
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  async getTokenExpiration(): Promise<number | null> {
    try {
      const accessToken = await this.oktaAuth.tokenManager.get('accessToken') as AccessToken;
      return accessToken ? accessToken.expiresAt * 1000 : null;
    } catch (error) {
      console.error('Failed to get token expiration:', error);
      return null;
    }
  }

  /**
   * Get the underlying Okta Auth client instance
   */
  getOktaAuthClient(): OktaAuth {
    return this.oktaAuth;
  }

  /**
   * Handle authentication callback after redirect
   */
  async handleCallback(): Promise<AuthenticationData> {
    try {
      await this.oktaAuth.handleLoginRedirect();
      return await this.getCurrentTokens();
    } catch (error) {
      console.error('Callback handling failed:', error);
      throw new Error(`Callback handling failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Sign in with redirect (web-specific)
   */
  async signInWithRedirect(originalUri?: string): Promise<void> {
    try {
      await this.oktaAuth.signInWithRedirect({
        originalUri: originalUri || window.location.href,
      });
    } catch (error) {
      console.error('Sign in with redirect failed:', error);
      throw new Error(`Sign in with redirect failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle silent authentication (if supported)
   */
  async silentAuthenticate(): Promise<AuthResult<AuthenticationData>> {
    try {
      const tokens = await this.oktaAuth.token.renewTokens();
      
      if (tokens.accessToken) {
        // Store the renewed tokens
        this.oktaAuth.tokenManager.setTokens(tokens);

        const authData = await this.getCurrentTokens();
        return {
          success: true,
          data: authData,
        };
      }

      return {
        success: false,
        error: 'No access token received from silent authentication',
      };
    } catch (error) {
      console.error('Silent authentication failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Silent authentication failed',
      };
    }
  }

  /**
   * Get user information from Okta
   */
  async getUserInfo(): Promise<any> {
    try {
      return await this.oktaAuth.getUser();
    } catch (error) {
      console.error('Failed to get user info:', error);
      throw new Error(`Failed to get user info: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Revoke tokens
   */
  async revokeTokens(accessToken?: string, refreshToken?: string): Promise<void> {
    try {
      // Get current access token if not provided
      const currentAccessToken = accessToken || await this.getAccessToken();

      if (currentAccessToken) {
        // Use the token manager to revoke tokens
        const accessTokenObj = await this.oktaAuth.tokenManager.get('accessToken') as AccessToken;
        if (accessTokenObj) {
          await this.oktaAuth.revokeAccessToken(accessTokenObj);
        }
      }

      // Note: Refresh token revocation would be implemented here if available
      if (refreshToken) {
        // Refresh token revocation is not commonly available in browser environments
        console.warn('Refresh token revocation not implemented for web platform');
      }
    } catch (error) {
      console.error('Token revocation failed:', error);
      throw new Error(`Token revocation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get current tokens as AuthenticationData
   */
  private async getCurrentTokens(): Promise<AuthenticationData> {
    try {
      const accessToken = await this.oktaAuth.tokenManager.get('accessToken') as AccessToken;
      const idToken = await this.oktaAuth.tokenManager.get('idToken') as IDToken;

      if (!accessToken) {
        throw new Error('No access token available');
      }

      return {
        accessToken: accessToken.accessToken,
        idToken: idToken?.idToken,
        expiresAt: accessToken.expiresAt * 1000, // Convert to milliseconds
        tokenType: 'Bearer',
        scope: accessToken.scopes,
      };
    } catch (error) {
      console.error('Failed to get current tokens:', error);
      throw new Error(`Failed to get current tokens: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Set up automatic token renewal
   */
  private setupTokenRenewal(): void {
    // Listen for token renewal events
    this.oktaAuth.tokenManager.on('renewed', (key: string, _newToken: any, _oldToken: any) => {
      console.log(`Token ${key} was renewed`);
    });

    this.oktaAuth.tokenManager.on('error', (error: any) => {
      console.error('Token manager error:', error);
    });

    // Start the token renewal service
    this.oktaAuth.start();
  }
}
