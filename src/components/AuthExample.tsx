/**
 * Authentication Example Component
 * 
 * Demonstrates usage of the new Clean Architecture authentication system.
 * Shows how to use the useAuthentication hook for auth operations.
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useAuthentication } from '@/hooks/useAuthentication';
import { useSecureStorage } from '@/hooks/useSecureStorage';

/**
 * Example component showing authentication and secure storage usage
 */
export function AuthExample() {
  const {
    isAuthenticated,
    user,
    isLoading,
    error,
    signIn,
    signOut,
    refreshAuth,
    clearError,
    getAccessToken,
  } = useAuthentication({
    autoCheck: true,
    autoRefresh: true,
    refreshBufferMinutes: 5,
  });

  const {
    storeData,
    getData,
    removeData,
    clearAll,
    isLoading: storageLoading,
    error: storageError,
    clearError: clearStorageError,
  } = useSecureStorage({
    enableLogging: __DEV__,
  });

  const handleSignIn = async () => {
    try {
      const success = await signIn();
      if (success) {
        Alert.alert('Success', 'Signed in successfully!');
      } else {
        Alert.alert('Error', 'Sign in failed');
      }
    } catch (error) {
      Alert.alert('Error', 'Sign in failed');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      Alert.alert('Success', 'Signed out successfully!');
    } catch (error) {
      Alert.alert('Error', 'Sign out failed');
    }
  };

  const handleRefreshAuth = async () => {
    try {
      const success = await refreshAuth();
      Alert.alert('Info', success ? 'Auth refreshed' : 'Refresh not needed');
    } catch (error) {
      Alert.alert('Error', 'Refresh failed');
    }
  };

  const handleGetToken = async () => {
    try {
      const token = await getAccessToken();
      if (token) {
        Alert.alert('Access Token', `Token: ${token.substring(0, 20)}...`);
      } else {
        Alert.alert('Info', 'No access token available');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get token');
    }
  };

  const handleStoreData = async () => {
    try {
      const testData = { message: 'Hello from secure storage!', timestamp: Date.now() };
      const success = await storeData('test_data', testData);
      Alert.alert('Storage', success ? 'Data stored successfully' : 'Storage failed');
    } catch (error) {
      Alert.alert('Error', 'Storage operation failed');
    }
  };

  const handleGetData = async () => {
    try {
      const data = await getData<{ message: string; timestamp: number }>('test_data');
      if (data) {
        Alert.alert('Retrieved Data', `Message: ${data.message}\nTime: ${new Date(data.timestamp).toLocaleString()}`);
      } else {
        Alert.alert('Info', 'No data found');
      }
    } catch (error) {
      Alert.alert('Error', 'Retrieval failed');
    }
  };

  const handleClearStorage = async () => {
    try {
      const success = await clearAll();
      Alert.alert('Storage', success ? 'Storage cleared' : 'Clear failed');
    } catch (error) {
      Alert.alert('Error', 'Clear operation failed');
    }
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Clean Architecture Auth Demo</Text>
      
      {/* Authentication Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Authentication Status</Text>
        <Text style={styles.statusText}>
          Status: {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
        </Text>
        {user && (
          <View style={styles.userInfo}>
            <Text style={styles.userText}>User: {user.name}</Text>
            <Text style={styles.userText}>Email: {user.email}</Text>
            <Text style={styles.userText}>Roles: {user.roles.join(', ')}</Text>
          </View>
        )}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Error: {error}</Text>
            <TouchableOpacity style={styles.clearButton} onPress={clearError}>
              <Text style={styles.clearButtonText}>Clear Error</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Authentication Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Authentication Actions</Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={[styles.button, !isAuthenticated && styles.primaryButton]} 
            onPress={handleSignIn}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Sign In</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, isAuthenticated && styles.dangerButton]} 
            onPress={handleSignOut}
            disabled={isLoading || !isAuthenticated}
          >
            <Text style={styles.buttonText}>Sign Out</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={styles.button} 
            onPress={handleRefreshAuth}
            disabled={isLoading || !isAuthenticated}
          >
            <Text style={styles.buttonText}>Refresh Auth</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.button} 
            onPress={handleGetToken}
            disabled={isLoading || !isAuthenticated}
          >
            <Text style={styles.buttonText}>Get Token</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Storage Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Secure Storage Actions</Text>
        {storageError && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Storage Error: {storageError}</Text>
            <TouchableOpacity style={styles.clearButton} onPress={clearStorageError}>
              <Text style={styles.clearButtonText}>Clear Error</Text>
            </TouchableOpacity>
          </View>
        )}
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={styles.button} 
            onPress={handleStoreData}
            disabled={storageLoading}
          >
            <Text style={styles.buttonText}>Store Data</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.button} 
            onPress={handleGetData}
            disabled={storageLoading}
          >
            <Text style={styles.buttonText}>Get Data</Text>
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity 
          style={[styles.button, styles.dangerButton]} 
          onPress={handleClearStorage}
          disabled={storageLoading}
        >
          <Text style={styles.buttonText}>Clear Storage</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  section: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  statusText: {
    fontSize: 16,
    marginBottom: 5,
    color: '#666',
  },
  userInfo: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f0f8ff',
    borderRadius: 5,
  },
  userText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  errorContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#ffe6e6',
    borderRadius: 5,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
    marginBottom: 5,
  },
  clearButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: '#f44336',
    borderRadius: 3,
  },
  clearButtonText: {
    color: 'white',
    fontSize: 12,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  button: {
    flex: 1,
    padding: 12,
    backgroundColor: '#2196f3',
    borderRadius: 5,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#4caf50',
  },
  dangerButton: {
    backgroundColor: '#f44336',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  loadingText: {
    fontSize: 18,
    textAlign: 'center',
    color: '#666',
  },
});
