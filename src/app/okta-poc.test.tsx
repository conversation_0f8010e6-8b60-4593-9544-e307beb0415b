/**
 * Tests for Okta PoC Screen
 */

import React from 'react';
import { render } from '@testing-library/react-native';
import OktaPoCScreen from './okta-poc';

// Mock expo-auth-session
jest.mock('expo-auth-session', () => ({
  makeRedirectUri: jest.fn(() => 'https://auth.expo.dev/@charlesrmajor/learning-coach-community'),
  useAuthRequest: jest.fn(() => [
    { clientId: 'test-client-id' }, // request
    null, // response
    jest.fn(), // promptAsync
  ]),
  useAutoDiscovery: jest.fn(() => ({
    authorizationEndpoint: 'https://test.okta.com/oauth2/authorize',
    tokenEndpoint: 'https://test.okta.com/oauth2/token',
  })),
}));

// Mock expo-web-browser
jest.mock('expo-web-browser', () => ({
  maybeCompleteAuthSession: jest.fn(),
}));

// Mock react-native-safe-area-context
jest.mock('react-native-safe-area-context', () => ({
  SafeAreaView: ({ children }: any) => children,
}));

// Mock environment variables
const mockEnv = {
  EXPO_PUBLIC_OKTA_ISSUER: 'https://integrator-5743111.okta.com/oauth2/default',
  EXPO_PUBLIC_OKTA_CLIENT_ID: '0oauipzrsuO08tffD697',
};

Object.defineProperty(process, 'env', {
  value: mockEnv,
});

describe('OktaPoCScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText } = render(<OktaPoCScreen />);
    
    expect(getByText('🔐 Okta Authentication PoC')).toBeTruthy();
    expect(getByText('Standalone proof of concept following Expo\'s official documentation')).toBeTruthy();
    expect(getByText('🚀 Login with Okta')).toBeTruthy();
  });

  it('displays configuration information', () => {
    const { getByText } = render(<OktaPoCScreen />);
    
    expect(getByText('Configuration')).toBeTruthy();
    expect(getByText('https://integrator-5743111.okta.com/oauth2/default')).toBeTruthy();
    expect(getByText('0oauipzr...')).toBeTruthy();
  });

  it('shows ready status when discovery and request are loaded', () => {
    const { getByText } = render(<OktaPoCScreen />);
    
    expect(getByText('🚀 Ready to authenticate!')).toBeTruthy();
  });

  it('displays how it works information', () => {
    const { getByText } = render(<OktaPoCScreen />);
    
    expect(getByText('How This Works')).toBeTruthy();
    expect(getByText('1. 🔍 Auto-discovers Okta endpoints using your domain')).toBeTruthy();
    expect(getByText('2. 🛠️ Creates authentication request with PKCE')).toBeTruthy();
    expect(getByText('3. 🌐 Uses Expo\'s auth proxy for redirect handling')).toBeTruthy();
    expect(getByText('4. 🔐 Opens Okta login in secure browser')).toBeTruthy();
    expect(getByText('5. ✅ Returns authorization code for token exchange')).toBeTruthy();
  });
});
