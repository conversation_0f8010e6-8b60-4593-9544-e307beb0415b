/**
 * Authentication Domain Models
 * 
 * Core domain models for authentication and user management.
 * These models represent the business entities and are platform-agnostic.
 */

/**
 * Core authentication data containing tokens and metadata
 */
export interface AuthenticationData {
  /** JWT access token for API requests */
  accessToken: string;
  /** Optional refresh token for token renewal */
  refreshToken?: string;
  /** Optional ID token containing user claims */
  idToken?: string;
  /** Token expiration timestamp in milliseconds */
  expiresAt: number;
  /** Token type (typically 'Bearer') */
  tokenType: 'Bearer';
  /** Optional scopes granted to the token */
  scope?: string[];
}

/**
 * User profile information from authentication provider
 */
export interface AuthenticatedUser {
  /** Unique user identifier */
  id: string;
  /** User's email address */
  email: string;
  /** User's display name */
  name: string;
  /** User's roles in the application */
  roles: string[];
  /** Optional tenant/organization identifier */
  tenant?: string;
  /** Optional groups the user belongs to */
  groups?: string[];
  /** Optional additional user attributes */
  attributes?: Record<string, any>;
}

/**
 * Current authentication state of the application
 */
export interface AuthenticationState {
  /** Whether the user is currently authenticated */
  isAuthenticated: boolean;
  /** Current authenticated user (null if not authenticated) */
  user: AuthenticatedUser | null;
  /** Whether authentication operations are in progress */
  isLoading: boolean;
  /** Current authentication error (null if no error) */
  error: string | null;
}

/**
 * Authentication operation result
 */
export interface AuthResult<T = any> {
  /** Whether the operation was successful */
  success: boolean;
  /** Result data (if successful) */
  data?: T;
  /** Error message (if failed) */
  error?: string;
}

/**
 * Token refresh result
 */
export interface TokenRefreshResult {
  /** Whether the refresh was successful */
  success: boolean;
  /** New authentication data (if successful) */
  authData?: AuthenticationData;
  /** Error message (if failed) */
  error?: string;
}

/**
 * Authentication configuration
 */
export interface AuthConfig {
  /** OAuth2/OIDC issuer URL */
  issuer: string;
  /** OAuth2/OIDC client ID */
  clientId: string;
  /** Redirect URI for authentication callback */
  redirectUri: string;
  /** OAuth2/OIDC scopes to request */
  scopes: string[];
  /** Additional configuration options */
  additionalParameters?: Record<string, string>;
}

/**
 * Authentication events that can be emitted
 */
export enum AuthEvent {
  SIGN_IN_SUCCESS = 'SIGN_IN_SUCCESS',
  SIGN_IN_FAILURE = 'SIGN_IN_FAILURE',
  SIGN_OUT = 'SIGN_OUT',
  TOKEN_REFRESH_SUCCESS = 'TOKEN_REFRESH_SUCCESS',
  TOKEN_REFRESH_FAILURE = 'TOKEN_REFRESH_FAILURE',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
}

/**
 * Authentication event data
 */
export interface AuthEventData {
  event: AuthEvent;
  user?: AuthenticatedUser;
  error?: string;
  timestamp: number;
}
