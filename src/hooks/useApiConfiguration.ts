/**
 * API Configuration Hook (Production)
 *
 * Custom hook for managing API configuration state and operations.
 * Handles UI state management for production use only.
 */

import { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { apiConfigService, type ApiConfig } from '@/services/apiConfigService';

export interface UseApiConfigurationReturn {
	// State
	apiKey: string;
	apiBaseUrl: string;
	storedConfig: ApiConfig;
	isLoading: boolean;

	// Actions
	setApiKey: (key: string) => void;
	setApiBaseUrl: (url: string) => void;
	loadStoredConfig: () => Promise<void>;
	saveApiKey: () => Promise<void>;
	saveApiBaseUrl: () => Promise<void>;
	deleteConfiguration: () => Promise<void>;
}

/**
 * Custom hook for API configuration management (Production)
 */
export function useApiConfiguration(): UseApiConfigurationReturn {
	// State
	const [apiKey, setApiKey] = useState('');
	const [apiBaseUrl, setApiBaseUrl] = useState('');
	const [storedConfig, setStoredConfig] = useState<ApiConfig>({ apiKey: null, apiBaseUrl: null });
	const [isLoading, setIsLoading] = useState(false);

	// Load stored configuration on mount
	useEffect(() => {
		loadStoredConfig();
	}, []);

	// Actions
	const loadStoredConfig = async () => {
		try {
			const config = await apiConfigService.loadApiConfig();
			setStoredConfig(config);
		} catch (error) {
			console.error('Failed to load stored API configuration:', error);
		}
	};

	const saveApiKey = async () => {
		setIsLoading(true);
		try {
			if (!apiKey || apiKey.trim().length === 0) {
				throw new Error('API key cannot be empty');
			}
			await apiConfigService.saveApiKey(apiKey);
			await loadStoredConfig();
			Alert.alert('Success', 'API key saved securely!');
		} catch (error: any) {
			Alert.alert('Error', error.message);
		} finally {
			setIsLoading(false);
		}
	};

	const saveApiBaseUrl = async () => {
		setIsLoading(true);
		try {
			if (!apiBaseUrl || apiBaseUrl.trim().length === 0) {
				throw new Error('API base URL cannot be empty');
			}
			// Basic URL validation
			try {
				new URL(apiBaseUrl);
			} catch {
				throw new Error('Invalid URL format');
			}
			await apiConfigService.saveApiBaseUrl(apiBaseUrl);
			await loadStoredConfig();
			Alert.alert('Success', 'API base URL saved securely!');
		} catch (error: any) {
			Alert.alert('Error', error.message);
		} finally {
			setIsLoading(false);
		}
	};

	const deleteConfiguration = async () => {
		setIsLoading(true);
		try {
			await apiConfigService.deleteApiConfig();
			setStoredConfig({ apiKey: null, apiBaseUrl: null });
			setApiKey('');
			setApiBaseUrl('');
			Alert.alert('Success', 'API configuration deleted successfully!');
		} catch (error: any) {
			Alert.alert('Error', `Failed to delete API configuration: ${error}`);
		} finally {
			setIsLoading(false);
		}
	};

	return {
		// State
		apiKey,
		apiBaseUrl,
		storedConfig,
		isLoading,

		// Actions
		setApiKey,
		setApiBaseUrl,
		loadStoredConfig,
		saveApiKey,
		saveApiBaseUrl,
		deleteConfiguration,
	};
}
