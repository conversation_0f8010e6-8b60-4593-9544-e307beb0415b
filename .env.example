# Okta Configuration
# Copy this file to .env.local and fill in your actual values

# Okta Domain (your Okta organization URL with /oauth2/default)
EXPO_PUBLIC_OKTA_ISSUER=https://integrator-5743111.okta.com/oauth2/default

# Okta Application Client ID
EXPO_PUBLIC_OKTA_CLIENT_ID=your-client-id-here

# App Scheme for deep linking (mobile only)
EXPO_PUBLIC_APP_SCHEME=learningcoachcommunity

# Development Notes:
# 1. The redirect URI is configured to use Expo's auth proxy: https://auth.expo.dev/@charlesrmajor/learning-coach-community
# 2. Make sure to configure this redirect URI in your Okta application settings
# 3. For production builds, you may want to use your own domain for the redirect URI
