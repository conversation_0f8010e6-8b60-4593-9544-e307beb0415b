{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5], "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-8d8bed1be38e0ad3f616.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/dev/stride/lcc/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-28ef362b205608acb8e0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/dev/stride/lcc/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-909418cfff1dd2da4580.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/dev/stride/lcc/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-ccf9b9ea1f1adf5f6b4a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/dev/stride/lcc/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [4]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-87703203b609d4a9e532.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/dev/stride/lcc/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-0b2891c6e616bbbd9fe7.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-51e10dd902f68d6e7275.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-9a93ea404553bb925ece.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-f7d93cba5346104bd004.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-b0c96942adc6aabb55e4.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-ad74dc4c5ee3b5e45029.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/dev/stride/lcc/android/app/.cxx/Debug/6z363s1j/arm64-v8a", "source": "/Users/<USER>/dev/stride/lcc/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}