/**
 * Service Factory Tests
 * 
 * Tests for the service factory and dependency injection system.
 */

import { Platform } from 'react-native';
import { ServiceFactory } from '../serviceFactory';
import { AuthenticationService } from '../authenticationService';
import { SecureStorageService } from '../secureStorageService';

// Mock React Native Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios', // Default to iOS for testing
  },
}));

// Mock the repositories
jest.mock('@/repositories/secureStorage/mobileSecureStorageRepository');
jest.mock('@/repositories/secureStorage/webSecureStorageRepository');
jest.mock('@/repositories/auth/expoAuthProvider');
jest.mock('@/repositories/auth/oktaWebAuthProvider');

describe('ServiceFactory', () => {
  beforeEach(() => {
    // Reset the factory before each test
    ServiceFactory.reset();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Platform Detection', () => {
    it('should detect mobile platform correctly', () => {
      (Platform.OS as any) = 'ios';
      expect(ServiceFactory.isMobilePlatform()).toBe(true);
      expect(ServiceFactory.isWebPlatform()).toBe(false);
      expect(ServiceFactory.getCurrentPlatform()).toBe('ios');

      (Platform.OS as any) = 'android';
      expect(ServiceFactory.isMobilePlatform()).toBe(true);
      expect(ServiceFactory.isWebPlatform()).toBe(false);
      expect(ServiceFactory.getCurrentPlatform()).toBe('android');
    });

    it('should detect web platform correctly', () => {
      (Platform.OS as any) = 'web';
      expect(ServiceFactory.isWebPlatform()).toBe(true);
      expect(ServiceFactory.isMobilePlatform()).toBe(false);
      expect(ServiceFactory.getCurrentPlatform()).toBe('web');
    });
  });

  describe('Service Creation', () => {
    it('should create authentication service for mobile platform', () => {
      (Platform.OS as any) = 'ios';
      
      const authService = ServiceFactory.createAuthenticationService();
      
      expect(authService).toBeInstanceOf(AuthenticationService);
    });

    it('should create authentication service for web platform', () => {
      (Platform.OS as any) = 'web';
      
      const authService = ServiceFactory.createAuthenticationService();
      
      expect(authService).toBeInstanceOf(AuthenticationService);
    });

    it('should create secure storage service', () => {
      const storageService = ServiceFactory.createSecureStorageService();
      
      expect(storageService).toBeInstanceOf(SecureStorageService);
    });

    it('should return same instance on multiple calls (singleton)', () => {
      const authService1 = ServiceFactory.createAuthenticationService();
      const authService2 = ServiceFactory.createAuthenticationService();
      
      expect(authService1).toBe(authService2);

      const storageService1 = ServiceFactory.createSecureStorageService();
      const storageService2 = ServiceFactory.createSecureStorageService();
      
      expect(storageService1).toBe(storageService2);
    });

    it('should create new instances after reset', () => {
      const authService1 = ServiceFactory.createAuthenticationService();
      
      ServiceFactory.reset();
      
      const authService2 = ServiceFactory.createAuthenticationService();
      
      expect(authService1).not.toBe(authService2);
    });
  });

  describe('Web-specific Services', () => {
    it('should create preferences repository on web platform', () => {
      (Platform.OS as any) = 'web';
      
      expect(() => {
        ServiceFactory.createPreferencesRepository();
      }).not.toThrow();
    });

    it('should throw error when creating preferences repository on mobile', () => {
      (Platform.OS as any) = 'ios';
      
      expect(() => {
        ServiceFactory.createPreferencesRepository();
      }).toThrow('Preferences repository is only available on web platform');
    });
  });

  describe('Service Info', () => {
    it('should return correct service info for mobile platform', () => {
      (Platform.OS as any) = 'ios';
      
      const info = ServiceFactory.getServiceInfo();
      
      expect(info).toEqual({
        platform: 'ios',
        authProvider: 'ExpoAuthProvider',
        storageProvider: 'MobileSecureStorageRepository',
        hasSessionStorage: false,
        hasPreferencesStorage: false,
      });
    });

    it('should return correct service info for web platform', () => {
      (Platform.OS as any) = 'web';
      
      const info = ServiceFactory.getServiceInfo();
      
      expect(info).toEqual({
        platform: 'web',
        authProvider: 'OktaWebAuthProvider',
        storageProvider: 'WebSecureStorageRepository',
        hasSessionStorage: true,
        hasPreferencesStorage: true,
      });
    });
  });

  describe('Environment Validation', () => {
    const originalEnv = process.env;

    beforeEach(() => {
      jest.resetModules();
      process.env = { ...originalEnv };
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('should validate environment with required variables', () => {
      process.env.EXPO_PUBLIC_OKTA_ISSUER = 'https://test.okta.com';
      process.env.EXPO_PUBLIC_OKTA_CLIENT_ID = 'test-client-id';
      
      const validation = ServiceFactory.validateEnvironment();
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should return errors for missing required variables', () => {
      delete process.env.EXPO_PUBLIC_OKTA_ISSUER;
      delete process.env.EXPO_PUBLIC_OKTA_CLIENT_ID;
      
      const validation = ServiceFactory.validateEnvironment();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('EXPO_PUBLIC_OKTA_ISSUER environment variable is required');
      expect(validation.errors).toContain('EXPO_PUBLIC_OKTA_CLIENT_ID environment variable is required');
    });

    it('should return warnings for missing optional variables', () => {
      (Platform.OS as any) = 'ios';
      process.env.EXPO_PUBLIC_OKTA_ISSUER = 'https://test.okta.com';
      process.env.EXPO_PUBLIC_OKTA_CLIENT_ID = 'test-client-id';
      delete process.env.EXPO_PUBLIC_APP_SCHEME;
      
      const validation = ServiceFactory.validateEnvironment();
      
      expect(validation.isValid).toBe(true);
      expect(validation.warnings).toContain('EXPO_PUBLIC_APP_SCHEME not set, using default scheme');
    });
  });

  describe('Test Service Creation', () => {
    it('should create test authentication service with mock dependencies', () => {
      const mockAuthProvider = {} as any;
      const mockStorageRepository = {} as any;
      const mockSessionRepository = {} as any;
      
      const testService = ServiceFactory.createTestAuthenticationService(
        mockAuthProvider,
        mockStorageRepository,
        mockSessionRepository
      );
      
      expect(testService).toBeInstanceOf(AuthenticationService);
    });

    it('should create test secure storage service with mock dependencies', () => {
      const mockStorageRepository = {} as any;
      
      const testService = ServiceFactory.createTestSecureStorageService(mockStorageRepository);
      
      expect(testService).toBeInstanceOf(SecureStorageService);
    });
  });
});
