/**
 * Tests for SimpleOktaLogin Component
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import SimpleOktaLogin from '../SimpleOktaLogin';

// Mock expo-auth-session
jest.mock('expo-auth-session', () => ({
  makeRedirectUri: jest.fn(() => 'https://auth.expo.dev/@charlesrmajor/learning-coach-community'),
  useAuthRequest: jest.fn(() => [
    { clientId: 'test-client-id' }, // request
    null, // response
    jest.fn(), // promptAsync
  ]),
  useAutoDiscovery: jest.fn(() => ({
    authorizationEndpoint: 'https://test.okta.com/oauth2/authorize',
    tokenEndpoint: 'https://test.okta.com/oauth2/token',
  })),
}));

// Mock expo-web-browser
jest.mock('expo-web-browser', () => ({
  maybeCompleteAuthSession: jest.fn(),
}));

// Mock environment variables
const mockEnv = {
  EXPO_PUBLIC_OKTA_ISSUER: 'https://integrator-5743111.okta.com/oauth2/default',
  EXPO_PUBLIC_OKTA_CLIENT_ID: '0oauipzrsuO08tffD697',
};

Object.defineProperty(process, 'env', {
  value: mockEnv,
});

describe('SimpleOktaLogin', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText } = render(<SimpleOktaLogin />);
    
    expect(getByText('Simple Okta Login')).toBeTruthy();
    expect(getByText('Following Expo\'s Official Documentation')).toBeTruthy();
    expect(getByText('Login with Okta')).toBeTruthy();
  });

  it('displays configuration information', () => {
    const { getByText } = render(<SimpleOktaLogin />);

    expect(getByText('Configuration:')).toBeTruthy();
    expect(getByText('Domain: https://integrator-5743111.okta.com/oauth2/default')).toBeTruthy();
    expect(getByText('Client ID: your-cli...')).toBeTruthy();
  });

  it('shows ready status initially', () => {
    const { getByText } = render(<SimpleOktaLogin />);
    
    expect(getByText('Ready to authenticate')).toBeTruthy();
  });

  it('calls promptAsync when login button is pressed', () => {
    const mockPromptAsync = jest.fn();
    const { useAuthRequest } = require('expo-auth-session');
    useAuthRequest.mockReturnValue([
      { clientId: 'test-client-id' }, // request
      null, // response
      mockPromptAsync, // promptAsync
    ]);

    const { getByText } = render(<SimpleOktaLogin />);
    const loginButton = getByText('Login with Okta');
    
    fireEvent.press(loginButton);
    
    expect(mockPromptAsync).toHaveBeenCalled();
  });

  it('calls onAuthSuccess when authentication succeeds', async () => {
    const mockOnAuthSuccess = jest.fn();
    const { useAuthRequest } = require('expo-auth-session');
    
    // Mock successful response
    const successResponse = {
      type: 'success',
      params: { code: 'test-auth-code' },
    };
    
    useAuthRequest.mockReturnValue([
      { clientId: 'test-client-id' }, // request
      successResponse, // response
      jest.fn(), // promptAsync
    ]);

    render(<SimpleOktaLogin onAuthSuccess={mockOnAuthSuccess} />);
    
    await waitFor(() => {
      expect(mockOnAuthSuccess).toHaveBeenCalledWith(successResponse);
    });
  });

  it('calls onAuthError when authentication fails', async () => {
    const mockOnAuthError = jest.fn();
    const { useAuthRequest } = require('expo-auth-session');
    
    // Mock error response
    const errorResponse = {
      type: 'error',
      error: { message: 'Authentication failed' },
    };
    
    useAuthRequest.mockReturnValue([
      { clientId: 'test-client-id' }, // request
      errorResponse, // response
      jest.fn(), // promptAsync
    ]);

    render(<SimpleOktaLogin onAuthError={mockOnAuthError} />);
    
    await waitFor(() => {
      expect(mockOnAuthError).toHaveBeenCalledWith('Authentication failed');
    });
  });

  it('shows success message when authentication succeeds', async () => {
    const { useAuthRequest } = require('expo-auth-session');
    
    // Mock successful response
    const successResponse = {
      type: 'success',
      params: { code: 'test-auth-code-12345' },
    };
    
    useAuthRequest.mockReturnValue([
      { clientId: 'test-client-id' }, // request
      successResponse, // response
      jest.fn(), // promptAsync
    ]);

    const { getByText } = render(<SimpleOktaLogin />);
    
    await waitFor(() => {
      expect(getByText('Authentication Successful!')).toBeTruthy();
      expect(getByText('✅ Success!')).toBeTruthy();
    });
  });
});
