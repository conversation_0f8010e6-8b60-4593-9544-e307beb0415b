import React, { createContext, useContext, useEffect, useState } from 'react';
import { useOktaAuthSession } from '../hooks/useOktaAuthSession';
import { User } from '../types/auth';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  signIn: () => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const {
    request,
    response,
    promptAsync,
    error: authError,
    isLoading: authLoading,
    exchangeCodeAsync,
    refreshAsync,
    revokeAsync,
  } = useOktaAuthSession();

  const [error, setError] = useState<string | null>(null);

  // Handle authentication response
  useEffect(() => {
    if (response?.type === 'success') {
      handleAuthSuccess();
    } else if (response?.type === 'error') {
      setError(response.error?.message || 'Authentication failed');
      setIsLoading(false);
    }
  }, [response]);

  // Handle auth errors
  useEffect(() => {
    if (authError) {
      setError(authError);
      setIsLoading(false);
    }
  }, [authError]);

  const handleAuthSuccess = async () => {
    try {
      if (!response?.params?.code) {
        throw new Error('No authorization code received');
      }

      console.log('🔐 [AuthContext] Exchanging code for tokens');
      const tokenResponse = await exchangeCodeAsync({
        code: response.params.code,
        extraParams: {},
      });

      if (tokenResponse?.accessToken) {
        console.log('🔐 [AuthContext] Token exchange successful');
        
        // Create user object from token response
        const userData: User = {
          id: tokenResponse.idToken ? 'user-id' : 'unknown', // You might want to decode the ID token
          email: '<EMAIL>', // Extract from ID token
          name: 'User Name', // Extract from ID token
          accessToken: tokenResponse.accessToken,
          refreshToken: tokenResponse.refreshToken || undefined,
          idToken: tokenResponse.idToken || undefined,
        };

        setUser(userData);
        setError(null);
      } else {
        throw new Error('No access token received');
      }
    } catch (err) {
      console.error('🔐 [AuthContext] Token exchange failed:', err);
      setError(err instanceof Error ? err.message : 'Token exchange failed');
    } finally {
      setIsLoading(false);
    }
  };

  const signIn = async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('🔐 [AuthContext] Starting sign-in process');
      
      if (!request) {
        throw new Error('Auth request not ready');
      }

      await promptAsync();
    } catch (err) {
      console.error('🔐 [AuthContext] Sign-in failed:', err);
      setError(err instanceof Error ? err.message : 'Sign-in failed');
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      console.log('🔐 [AuthContext] Starting sign-out process');
      
      if (user?.accessToken && revokeAsync) {
        await revokeAsync({
          token: user.accessToken,
        }, {
          revocationEndpoint: 'https://integrator-5743111.okta.com/oauth2/default/v1/revoke',
        });
      }

      setUser(null);
      setError(null);
      console.log('🔐 [AuthContext] Sign-out successful');
    } catch (err) {
      console.error('🔐 [AuthContext] Sign-out failed:', err);
      // Still clear the user even if revocation fails
      setUser(null);
      setError(err instanceof Error ? err.message : 'Sign-out failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Check for existing session on mount
  useEffect(() => {
    // For now, just set loading to false
    // In a real app, you'd check for stored tokens and validate them
    setIsLoading(false);
  }, []);

  const value: AuthContextType = {
    user,
    isLoading: isLoading || authLoading,
    error: error || authError,
    signIn,
    signOut,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
